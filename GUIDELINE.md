# 项目名称：团子

我们现在正在打算制作一个项目，有关团建所有相关事务的集成手机APP

### 标语：
*"我们团，你们玩"*

### 目标群体
需要在团队层面进行娱乐行为的组织规划人员，包括：
1. *企业活动负责人*：公司年会、团建
2. *学校活动负责人*：大学班会、娱乐性质组织活动、课堂活跃气氛
3. *学生*：宿舍、课间、朋友聚会
4. *娱乐活动企业*：直接使用软件作为商业辅助工具

### 开发周期
2个月，从6月下旬到9月上旬

### 资金预算
初步确定500元，需要进一步协商确认

### 发售平台
国内、国外安卓手机应用市场

### 盈利模式
广告（付费无广告）、订阅或买断（付费使用完整功能）
具体金额因情况而定

**免费版**：
1. 房间限制：2小时，10人
2. 游戏限制：限制使用大部分游戏功能
3. 环节设计不可使用选择性环节，仅开放常驻环节设计

**Pro版**：
1. 房间限制：24小时，500人
2. 游戏限制：限制使用小部分复杂游戏功能

**Max版**
1.房间限制：72小时，无上限限制（实际上后台设计是2000人）
    
### 产品目标

实现常见团建行为可能会涉及的大多数功能，包括：

**房间**：房间系统是整个产品最核心的开发思路。房间可以是自由房间，也可以是预先设计好，由若干环节组成的预设房间

- *启动*：直接启动一个房间，快速启动，使用最简单的配置，比如仅支持30 -> 45分钟、仅支持4人在线等；支持高端启动，会需要更复杂的配置，而且对于房间内支持时长、在线人数等有更大的容量，同时需要付费解锁。
- *预约*：预约房间，约定房间启动时间、地点等信息（可自定义）。如果房间在预约预定开始时间之后15分钟之内无人加入，则会自动关闭（可以在此基础上，将预约入口通过微信、QQ等方式分享）
- *加入*：加入一个已经存在的房间，如果距离关闭仅有15分钟则不可加入

**日历**：用户会具有一个日历，记录着自已需要参加的事件以及对应时间。

**签到**：支持签到系统，通过扫码完成，预先预约的人员可以使得主持人可以明确预约人员的出现率

**点名**：支持点名系统，可以在已签到且在场人员名单中、或者仅仅是在场人员名单中进行随机点名、多个点名等

**分组**（pro）：支持分组，包括随机分组、按男女分组、按特定信息分组等

**环节**：“环节”系统是本项目的又一个重点功能。支持用户提前在APP自定义“环节”，并且在该用户作为主持人的房间中直接调用。房间可以根据该“环节”是否满足房间特点而执行，用户之间可以互相共享设计好的“环节”。

- *环节设计器*：提供一个设计环节的界面，可以允许用户自由组合环节进行设计
- *环节分类*：分为不同功能用途的环节：
*常驻环节*：
    1. 准备环节：准备阶段，用作提前进行提示、说明
    2. 开始环节：开始阶段，环节本身逻辑正式开始
    3. 操作环节：操作阶段，允许参与者进行操作，参与环节逻辑
    4. 判定环节：判定阶段，禁止参与者操作，环节逻辑将参考玩家的参与内容进行判定
    5. 结束环节：结束阶段，根据判定结果，进行总结
*选择性环节*：
    - 暂停环节：暂停所有操作，作为一个可以随时开始和结束的阶段，用于执行活性的暂停环节，参与者不可操作，不执行环节逻辑
    - 发言环节：与暂停环节类似，但是开放了发言接口。
    - 自定义环节：前文提到的有关参与者操作的操作权限、环节逻辑、以及额外的时间流程等接口将开放给设计者，
- *系统级环节预设*：
根据环节的不同设计，会将不同的环节预设分为不同类型
**游戏**：团子APP的主要功能，实现常见的游戏
    1. 即时判断类：你画我猜，你描述我猜，猜歌名，猜成语，飞花令等*可以抽象为一个或若干个即时判断猜测内容与谜底是否匹配的逻辑：对吗？不对！*
    2. 延时判断类：狼人杀，海龟汤，谁是卧底，三国杀等*理论上在进行一次猜测之后，不能直接揭晓谜底，或者不能直接揭晓所有谜底的游戏*
    （由于这是该项目的主要目的，所以游戏种类越多越好）
**社交**：
    1. 聊天，自定义背景音乐、背景图（可存在预设）
    2. 页面重定向，跳转到另一个主持人指定用于团建活动的页面
*计分*：环节的一个重要的接口是计分接口，一旦有任何环节涉及到计分接口的实现，页面就会开始计分，在房间结束之后显示在成绩板上

**回忆**：将“环节”部分可量化参数进行总结，最终生成一个房间活动总结。回忆可以直接自动生成，也让主持人自己手动编辑。最终用户可以

**反馈**：异常的生成、存在投诉、网络不稳定、或者仅仅是进行时长比较长的房间都会触发反馈系统，有效不为空的反馈会呈现给主持人，有关系统及别的异常会被序列化为log文件显示在后端的监测文件中。

### 区分特征：
本产品相比于同类型产品具有较大区分度，主要体现在以下维度：
1. *与团体APP相比*：市面上大多数产品针对的都是公司本身的活动，日程表，会议安排，或者其它功能，仅仅针对与娱乐使用的团建APP并不多见，而本产品融合了团体APP在功能和规划能力上的优势。
2. *与团建游戏APP相比*：国内较流行的团建APP“会玩”是一个同领域内有力的竞争对手，但是侧重点有所不同。“会玩”则更倾向于是一种大型线上网络应用，重点关注玩家之间远程交互性，随机组队等行为，具体盈利方式可以专注于大量的广告投放、虚拟货币等；而本产品更侧重的是有限空间和资源使用内，通过简单的网络交互实现面对面团建游戏，主要是一个工具应用，在随机组队方面并不支持，相应的盈利方式也会倾向于轻广告，以及付费获取更多功能，而未来拓展海外市场则会更倾向于买断制。


### 开发团队：
两名开发者，一个设计师

*程序*： 
1. 开发者A，在校大学生，准大二，这个GUIDELINE.md文件的编写者。具有一定的Web开发、Unity项目开发经验，使用过Django, Flask, Vue, Svelte等技术栈
2. 开发者B，在校大学生，准大二，具有一定的Web开发经验，使用过Flask, Django技术栈
3. 团队依赖Cursor和Augment等半自动化AI软件进行辅助开发

*美术*：

设计师为动画专业，不参加主要任务，会偶尔指点页面设计，提供美术意见

### 技术栈
主要是手机APP，面向安卓市场，并且重视将算力负担平分到客户端，降低服务端业务成本
前端：React native
后端：Django
数据库：初期开发使用SQLite，后期转向PostgreSQL


