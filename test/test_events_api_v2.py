"""
Tests for Event Designer V2 API functionality
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from events.models import EventTemplate, EventStep

User = get_user_model()


class EventStepUpdateAPITest(TestCase):
    """Test EventStep update functionality"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            password='testpass123'
        )
        
        # Create JWT token for authentication
        refresh = RefreshToken.for_user(self.user)
        self.token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
        
        # Create test template and steps
        self.template = EventTemplate.objects.create(
            name='Test Template',
            description='Test Description',
            creator=self.user
        )
        
        self.step = EventStep.objects.create(
            template=self.template,
            name='Test Step',
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
    
    def test_update_step_success(self):
        """Test successful step update"""
        url = reverse('eventstep-detail', kwargs={'pk': self.step.pk})
        data = {
            'name': 'Updated Step Name',
            'step_type': EventStep.STEP_FREE_CHAT,
            'duration': 600
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.step.refresh_from_db()
        self.assertEqual(self.step.name, 'Updated Step Name')
        self.assertEqual(self.step.step_type, EventStep.STEP_FREE_CHAT)
        self.assertEqual(self.step.duration, 600)
    
    def test_update_step_unauthorized(self):
        """Test step update by non-owner"""
        # Create step owned by other user
        other_template = EventTemplate.objects.create(
            name='Other Template',
            creator=self.other_user
        )
        other_step = EventStep.objects.create(
            template=other_template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
        
        url = reverse('eventstep-detail', kwargs={'pk': other_step.pk})
        data = {'name': 'Hacked Name'}
        
        response = self.client.patch(url, data, format='json')
        
        # Should return 404 because queryset filters by owner
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_update_step_validation(self):
        """Test step update with invalid data"""
        url = reverse('eventstep-detail', kwargs={'pk': self.step.pk})
        data = {
            'step_type': 'INVALID_TYPE',
            'duration': -100
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_delete_step_success(self):
        """Test successful step deletion"""
        url = reverse('eventstep-detail', kwargs={'pk': self.step.pk})

        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(EventStep.objects.filter(pk=self.step.pk).exists())

    def test_delete_step_unauthorized(self):
        """Test step deletion by non-owner"""
        # Create step owned by other user
        other_template = EventTemplate.objects.create(
            name='Other Template',
            creator=self.other_user
        )
        other_step = EventStep.objects.create(
            template=other_template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )

        url = reverse('eventstep-detail', kwargs={'pk': other_step.pk})

        response = self.client.delete(url)

        # Should return 404 because queryset filters by owner
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        # Step should still exist
        self.assertTrue(EventStep.objects.filter(pk=other_step.pk).exists())


class EventStepReorderAPITest(TestCase):
    """Test EventStep reordering functionality"""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # Create JWT token for authentication
        refresh = RefreshToken.for_user(self.user)
        self.token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
        
        # Create test template with multiple steps
        self.template = EventTemplate.objects.create(
            name='Test Template',
            description='Test Description',
            creator=self.user
        )
        
        self.step1 = EventStep.objects.create(
            template=self.template,
            name='Step 1',
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
        
        self.step2 = EventStep.objects.create(
            template=self.template,
            name='Step 2',
            order=2,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=600
        )
        
        self.step3 = EventStep.objects.create(
            template=self.template,
            name='Step 3',
            order=3,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
    
    def test_reorder_steps_success(self):
        """Test successful step reordering"""
        url = reverse('eventtemplate-reorder-steps', kwargs={'pk': self.template.pk})
        # Reorder: step3, step1, step2
        data = {
            'step_ids': [self.step3.id, self.step1.id, self.step2.id]
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check new order
        self.step1.refresh_from_db()
        self.step2.refresh_from_db()
        self.step3.refresh_from_db()
        
        self.assertEqual(self.step3.order, 1)
        self.assertEqual(self.step1.order, 2)
        self.assertEqual(self.step2.order, 3)
    
    def test_reorder_steps_missing_ids(self):
        """Test reordering with missing step IDs"""
        url = reverse('eventtemplate-reorder-steps', kwargs={'pk': self.template.pk})
        data = {
            'step_ids': [self.step1.id, self.step2.id]  # Missing step3
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('do not match', response.data['error'])
    
    def test_reorder_steps_invalid_ids(self):
        """Test reordering with invalid step IDs"""
        url = reverse('eventtemplate-reorder-steps', kwargs={'pk': self.template.pk})
        data = {
            'step_ids': [self.step1.id, self.step2.id, 999]  # Invalid ID
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_reorder_steps_empty_list(self):
        """Test reordering with empty step_ids"""
        url = reverse('eventtemplate-reorder-steps', kwargs={'pk': self.template.pk})
        data = {'step_ids': []}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('step_ids is required', response.data['error'])


class EventStepModelTest(TestCase):
    """Test EventStep model with name field"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.template = EventTemplate.objects.create(
            name='Test Template',
            creator=self.user
        )
    
    def test_step_str_with_custom_name(self):
        """Test __str__ method with custom name"""
        step = EventStep.objects.create(
            template=self.template,
            name='Custom Step Name',
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
        
        expected = "Step 1: Custom Step Name for 'Test Template'"
        self.assertEqual(str(step), expected)
    
    def test_step_str_without_custom_name(self):
        """Test __str__ method without custom name"""
        step = EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
        
        expected = "Step 1: 游戏：你画我猜 for 'Test Template'"
        self.assertEqual(str(step), expected)
    
    def test_step_ordering(self):
        """Test step ordering by template and order"""
        step1 = EventStep.objects.create(
            template=self.template,
            order=2,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=300
        )
        step2 = EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=300
        )

        # Filter by template to avoid interference from preset data
        steps = list(EventStep.objects.filter(template=self.template))
        self.assertEqual(steps[0], step2)  # order=1 comes first
        self.assertEqual(steps[1], step1)  # order=2 comes second
