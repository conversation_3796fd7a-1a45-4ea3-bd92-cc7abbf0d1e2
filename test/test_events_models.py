"""
全面的事件模型测试

测试events.models中的所有模型，包括：
- EventTemplate模型：模板创建、权限管理
- EventStep模型：步骤类型、配置验证、订阅权限
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError

from events.models import EventTemplate, EventStep
from core.models import User

User = get_user_model()


class EventTemplateModelTest(TestCase):
    """测试EventTemplate模型的功能"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        self.other_user = User.objects.create_user(
            username='otheruser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
    
    def test_event_template_creation(self):
        """测试事件模板创建"""
        template = EventTemplate.objects.create(
            name='Test Template',
            description='A test template',
            creator=self.user
        )
        
        self.assertEqual(template.name, 'Test Template')
        self.assertEqual(template.description, 'A test template')
        self.assertEqual(template.creator, self.user)
        self.assertIsNotNone(template.created_at)
    
    def test_event_template_str_representation(self):
        """测试事件模板字符串表示"""
        template = EventTemplate.objects.create(
            name='Test Template',
            description='A test template',
            creator=self.user
        )
        
        expected = "'Test Template' by testuser"
        self.assertEqual(str(template), expected)
    
    def test_event_template_with_empty_description(self):
        """测试创建没有描述的事件模板"""
        template = EventTemplate.objects.create(
            name='No Description Template',
            creator=self.user
        )
        
        self.assertEqual(template.name, 'No Description Template')
        self.assertEqual(template.description, '')
        self.assertEqual(template.creator, self.user)
    
    def test_event_template_name_length(self):
        """测试事件模板名称长度"""
        # 测试正常长度的名称
        normal_name = 'Normal Template Name'
        template = EventTemplate.objects.create(
            name=normal_name,
            creator=self.user
        )
        self.assertEqual(template.name, normal_name)

        # 测试较长的名称（在CharField max_length范围内）
        long_name = 'A' * 100  # 正好100个字符
        template_long = EventTemplate.objects.create(
            name=long_name,
            creator=self.user
        )
        self.assertEqual(template_long.name, long_name)


class EventStepModelTest(TestCase):
    """测试EventStep模型的功能"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        self.template = EventTemplate.objects.create(
            name='Test Template',
            description='A test template',
            creator=self.user
        )
    
    def test_event_step_creation_with_defaults(self):
        """测试事件步骤创建时的默认值"""
        step = EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY
        )
        
        self.assertEqual(step.template, self.template)
        self.assertEqual(step.order, 1)
        self.assertEqual(step.step_type, EventStep.STEP_GAME_PICTIONARY)
        self.assertEqual(step.duration, 300)  # 默认5分钟
        self.assertEqual(step.configuration, {})
        self.assertEqual(step.name, '')
    
    def test_event_step_with_custom_name(self):
        """测试带自定义名称的事件步骤"""
        step = EventStep.objects.create(
            template=self.template,
            name='Custom Pictionary Round',
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=600
        )
        
        self.assertEqual(step.name, 'Custom Pictionary Round')
        self.assertEqual(step.duration, 600)
    
    def test_event_step_str_representation(self):
        """测试事件步骤字符串表示"""
        # 测试有自定义名称的步骤
        step_with_name = EventStep.objects.create(
            template=self.template,
            name='Custom Step Name',
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY
        )
        
        expected = "Step 1: Custom Step Name for 'Test Template'"
        self.assertEqual(str(step_with_name), expected)
        
        # 测试没有自定义名称的步骤
        step_without_name = EventStep.objects.create(
            template=self.template,
            order=2,
            step_type=EventStep.STEP_FREE_CHAT
        )
        
        expected = "Step 2: 自由讨论 for 'Test Template'"
        self.assertEqual(str(step_without_name), expected)
    
    def test_event_step_types(self):
        """测试所有事件步骤类型"""
        # 测试基础步骤类型
        basic_steps = [
            EventStep.STEP_GAME_PICTIONARY,
            EventStep.STEP_FREE_CHAT
        ]
        
        for i, step_type in enumerate(basic_steps, 1):
            step = EventStep.objects.create(
                template=self.template,
                order=i,
                step_type=step_type
            )
            self.assertEqual(step.step_type, step_type)
        
        # 测试高级步骤类型
        premium_steps = [
            EventStep.STEP_PAUSE,
            EventStep.STEP_SPEECH,
            EventStep.STEP_CUSTOM,
            EventStep.STEP_POLL,
            EventStep.STEP_QNA
        ]
        
        for i, step_type in enumerate(premium_steps, 10):
            step = EventStep.objects.create(
                template=self.template,
                order=i,
                step_type=step_type
            )
            self.assertEqual(step.step_type, step_type)
    
    def test_event_step_premium_types_identification(self):
        """测试高级步骤类型识别"""
        # 测试基础步骤类型不在高级类型中
        self.assertNotIn(EventStep.STEP_GAME_PICTIONARY, EventStep.PREMIUM_STEP_TYPES)
        self.assertNotIn(EventStep.STEP_FREE_CHAT, EventStep.PREMIUM_STEP_TYPES)
        
        # 测试高级步骤类型在高级类型中
        self.assertIn(EventStep.STEP_PAUSE, EventStep.PREMIUM_STEP_TYPES)
        self.assertIn(EventStep.STEP_SPEECH, EventStep.PREMIUM_STEP_TYPES)
        self.assertIn(EventStep.STEP_CUSTOM, EventStep.PREMIUM_STEP_TYPES)
        self.assertIn(EventStep.STEP_POLL, EventStep.PREMIUM_STEP_TYPES)
        self.assertIn(EventStep.STEP_QNA, EventStep.PREMIUM_STEP_TYPES)
    
    def test_event_step_configuration_field(self):
        """测试事件步骤配置字段"""
        # 测试空配置
        step = EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY
        )
        self.assertEqual(step.configuration, {})
        
        # 测试复杂配置
        complex_config = {
            'rounds': 3,
            'time_per_round': 120,
            'words': ['apple', 'banana', 'cherry'],
            'settings': {
                'allow_hints': True,
                'max_players': 8
            }
        }
        
        step_with_config = EventStep.objects.create(
            template=self.template,
            order=2,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            configuration=complex_config
        )
        
        self.assertEqual(step_with_config.configuration, complex_config)
        self.assertEqual(step_with_config.configuration['rounds'], 3)
        self.assertEqual(step_with_config.configuration['settings']['allow_hints'], True)
    
    def test_event_step_ordering(self):
        """测试事件步骤排序"""
        # 创建多个步骤
        step3 = EventStep.objects.create(
            template=self.template,
            order=3,
            step_type=EventStep.STEP_FREE_CHAT
        )
        
        step1 = EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY
        )
        
        step2 = EventStep.objects.create(
            template=self.template,
            order=2,
            step_type=EventStep.STEP_PAUSE
        )
        
        # 获取排序后的步骤
        ordered_steps = list(EventStep.objects.filter(template=self.template).order_by('order'))
        
        self.assertEqual(len(ordered_steps), 3)
        self.assertEqual(ordered_steps[0], step1)
        self.assertEqual(ordered_steps[1], step2)
        self.assertEqual(ordered_steps[2], step3)
    
    def test_event_step_duration_validation(self):
        """测试事件步骤持续时间验证"""
        # 测试正常持续时间
        step = EventStep.objects.create(
            template=self.template,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=600  # 10分钟
        )
        self.assertEqual(step.duration, 600)
        
        # 测试最小持续时间
        step_min = EventStep.objects.create(
            template=self.template,
            order=2,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=1  # 1秒
        )
        self.assertEqual(step_min.duration, 1)
        
        # 测试很长的持续时间
        step_long = EventStep.objects.create(
            template=self.template,
            order=3,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=7200  # 2小时
        )
        self.assertEqual(step_long.duration, 7200)
    
    def test_event_step_name_length(self):
        """测试事件步骤名称长度"""
        # 测试正常长度的名称
        normal_name = 'Normal Step Name'
        step = EventStep.objects.create(
            template=self.template,
            name=normal_name,
            order=1,
            step_type=EventStep.STEP_GAME_PICTIONARY
        )
        self.assertEqual(step.name, normal_name)

        # 测试较长的名称（在CharField max_length范围内）
        long_name = 'A' * 100  # 正好100个字符
        step_long = EventStep.objects.create(
            template=self.template,
            name=long_name,
            order=2,
            step_type=EventStep.STEP_FREE_CHAT
        )
        self.assertEqual(step_long.name, long_name)


class EventStepSubscriptionTest(TestCase):
    """测试事件步骤订阅权限相关功能"""
    
    def setUp(self):
        """设置测试数据"""
        self.free_user = User.objects.create_user(
            username='freeuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.pro_user = User.objects.create_user(
            username='prouser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO
        )
        
        self.max_user = User.objects.create_user(
            username='maxuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_MAX
        )
    
    def test_basic_step_types_available_to_all(self):
        """测试基础步骤类型对所有用户可用"""
        basic_steps = [EventStep.STEP_GAME_PICTIONARY, EventStep.STEP_FREE_CHAT]
        
        for step_type in basic_steps:
            self.assertNotIn(step_type, EventStep.PREMIUM_STEP_TYPES)
    
    def test_premium_step_types_identification(self):
        """测试高级步骤类型正确识别"""
        premium_steps = [
            EventStep.STEP_PAUSE,
            EventStep.STEP_SPEECH,
            EventStep.STEP_CUSTOM,
            EventStep.STEP_POLL,
            EventStep.STEP_QNA
        ]
        
        for step_type in premium_steps:
            self.assertIn(step_type, EventStep.PREMIUM_STEP_TYPES)
    
    def test_step_type_choices_completeness(self):
        """测试步骤类型选择的完整性"""
        # 确保所有定义的步骤类型都在选择列表中
        defined_types = {
            EventStep.STEP_GAME_PICTIONARY,
            EventStep.STEP_FREE_CHAT,
            EventStep.STEP_PAUSE,
            EventStep.STEP_SPEECH,
            EventStep.STEP_CUSTOM,
            EventStep.STEP_POLL,
            EventStep.STEP_QNA
        }
        
        choice_types = {choice[0] for choice in EventStep.STEP_TYPE_CHOICES}
        
        self.assertEqual(defined_types, choice_types)
    
    def test_step_type_display_names(self):
        """测试步骤类型显示名称"""
        step_type_names = dict(EventStep.STEP_TYPE_CHOICES)
        
        self.assertEqual(step_type_names[EventStep.STEP_GAME_PICTIONARY], '游戏：你画我猜')
        self.assertEqual(step_type_names[EventStep.STEP_FREE_CHAT], '自由讨论')
        self.assertEqual(step_type_names[EventStep.STEP_PAUSE], '暂停环节')
        self.assertEqual(step_type_names[EventStep.STEP_SPEECH], '发言环节')
        self.assertEqual(step_type_names[EventStep.STEP_CUSTOM], '自定义环节')
        self.assertEqual(step_type_names[EventStep.STEP_POLL], '投票环节')
        self.assertEqual(step_type_names[EventStep.STEP_QNA], '问答环节')
