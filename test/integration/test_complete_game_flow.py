"""
End-to-end integration tests for complete game flows.
"""

import pytest
import json
import asyncio
from django.contrib.auth import get_user_model
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from core.models import Room, RoomParticipant, RoomState, UserState
from core.consumers import RoomConsumer
from core.services.room_manager import room_manager
from events.models import EventTemplate, EventStep

User = get_user_model()


@pytest.mark.integration
@pytest.mark.asyncio
class TestCompleteGameFlow:
    """Test complete game flows from start to finish."""

    @pytest.fixture(autouse=True)
    async def setup_method(self):
        """Set up test data for each test method."""
        # Create test users
        self.host_user = await database_sync_to_async(User.objects.create_user)(
            username='flow_host',
            password='testpass123',
            subscription_level='Pro'
        )
        
        self.participant1 = await database_sync_to_async(User.objects.create_user)(
            username='flow_participant1',
            password='testpass123',
            subscription_level='Free'
        )
        
        self.participant2 = await database_sync_to_async(User.objects.create_user)(
            username='flow_participant2',
            password='testpass123',
            subscription_level='Free'
        )
        
        # Create comprehensive test template
        @database_sync_to_async
        def create_template():
            template = EventTemplate.objects.create(
                name='Complete Game Template',
                description='Template for complete game flow testing',
                creator=self.host_user
            )
            
            # Add multiple steps for comprehensive testing
            EventStep.objects.create(
                template=template,
                order=1,
                step_type=EventStep.STEP_GAME_PICTIONARY,
                duration=300,
                name='Pictionary Round 1'
            )
            
            EventStep.objects.create(
                template=template,
                order=2,
                step_type=EventStep.STEP_FREE_CHAT,
                duration=180,
                name='Discussion Break'
            )
            
            EventStep.objects.create(
                template=template,
                order=3,
                step_type=EventStep.STEP_GAME_PICTIONARY,
                duration=300,
                name='Pictionary Round 2'
            )
            
            return template
        
        self.template = await create_template()
        
        # Set up API client
        self.api_client = APIClient()
    
    async def teardown_method(self):
        """Clean up after each test method."""
        await database_sync_to_async(Room.objects.filter(room_code__startswith='FLOW').delete)()
        await database_sync_to_async(EventTemplate.objects.filter(name__contains='Complete Game').delete)()
        await database_sync_to_async(User.objects.filter(username__startswith='flow_').delete)()
    
    def get_authenticated_api_client(self, user):
        """Get an authenticated API client for the given user."""
        refresh = RefreshToken.for_user(user)
        client = APIClient()
        client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')
        return client
    
    async def get_authenticated_websocket(self, user, room_code):
        """Get an authenticated WebSocket communicator."""
        refresh = RefreshToken.for_user(user)
        token = str(refresh.access_token)
        
        communicator = WebsocketCommunicator(
            RoomConsumer.as_asgi(),
            f"/ws/room/{room_code}/?token={token}"
        )
        
        return communicator
    
    @pytest.mark.asyncio
    async def test_complete_game_lifecycle(self):
        """Test a complete game from creation to closure."""
        # Step 1: Host creates room via API
        host_client = self.get_authenticated_api_client(self.host_user)
        create_response = host_client.post('/api/rooms/create/', {
            'template_id': self.template.id
        })
        
        assert create_response.status_code == 201
        room_code = create_response.data['room_code']
        
        # Step 2: Participants join via API
        p1_client = self.get_authenticated_api_client(self.participant1)
        join_response1 = p1_client.post('/api/rooms/join/', {
            'room_code': room_code
        })
        assert join_response1.status_code == 200
        
        p2_client = self.get_authenticated_api_client(self.participant2)
        join_response2 = p2_client.post('/api/rooms/join/', {
            'room_code': room_code
        })
        assert join_response2.status_code == 200
        
        # Step 3: All users connect via WebSocket
        host_ws = await self.get_authenticated_websocket(self.host_user, room_code)
        p1_ws = await self.get_authenticated_websocket(self.participant1, room_code)
        p2_ws = await self.get_authenticated_websocket(self.participant2, room_code)
        
        await host_ws.connect()
        await p1_ws.connect()
        await p2_ws.connect()
        
        # Clear initial messages
        await host_ws.receive_json_from()
        await p1_ws.receive_json_from()
        await p2_ws.receive_json_from()
        
        # Step 4: Host starts the game
        await host_ws.send_json_to({
            'action': 'start_game',
            'payload': {}
        })
        
        # All users should receive state change
        host_msg = await host_ws.receive_json_from()
        p1_msg = await p1_ws.receive_json_from()
        p2_msg = await p2_ws.receive_json_from()
        
        assert host_msg['type'] == 'room.state'
        assert host_msg['payload']['status'] == RoomState.IN_PROGRESS
        assert p1_msg['payload']['status'] == RoomState.IN_PROGRESS
        assert p2_msg['payload']['status'] == RoomState.IN_PROGRESS
        
        # Step 5: Start first step (Pictionary)
        await host_ws.send_json_to({
            'action': 'next_step',
            'payload': {}
        })
        
        # Should receive step start notification
        step_msg = await host_ws.receive_json_from()
        assert step_msg['type'] in ['step.started', 'game.pictionary.started']
        
        # Step 6: Simulate some game activity
        # Send chat message
        await p1_ws.send_json_to({
            'action': 'chat_message',
            'payload': {'message': 'Is it a cat?'}
        })
        
        # All should receive chat
        await host_ws.receive_json_from()
        await p1_ws.receive_json_from()
        await p2_ws.receive_json_from()
        
        # Send drawing data
        await host_ws.send_json_to({
            'action': 'send_drawing',
            'payload': {
                'type': 'stroke',
                'points': [{'x': 100, 'y': 100}],
                'color': '#000000'
            }
        })
        
        # Participants should receive drawing
        await p1_ws.receive_json_from()
        await p2_ws.receive_json_from()
        
        # Step 7: Move to next step (Free Chat)
        await host_ws.send_json_to({
            'action': 'next_step',
            'payload': {}
        })
        
        # Should receive step change
        step_msg = await host_ws.receive_json_from()
        assert step_msg['type'] in ['step.started', 'chat.started']
        
        # Step 8: Move to final step
        await host_ws.send_json_to({
            'action': 'next_step',
            'payload': {}
        })
        
        # Step 9: End the game (transition to REVIEW)
        await host_ws.send_json_to({
            'action': 'end_game',
            'payload': {}
        })
        
        # Should receive state change to REVIEW
        review_msg = await host_ws.receive_json_from()
        assert review_msg['type'] == 'room.state'
        assert review_msg['payload']['status'] == RoomState.ENDED
        
        # Step 10: Close the room
        await host_ws.send_json_to({
            'action': 'close_room',
            'payload': {}
        })
        
        # Should receive final state change
        close_msg = await host_ws.receive_json_from()
        assert close_msg['type'] == 'room.state'
        assert close_msg['payload']['status'] == RoomState.CLOSED
        
        # Cleanup
        await host_ws.disconnect()
        await p1_ws.disconnect()
        await p2_ws.disconnect()
        
        # Verify final room state via API
        final_response = host_client.get(f'/api/rooms/{room_code}/')
        assert final_response.status_code == 200
        assert final_response.data['status'] == RoomState.CLOSED
    
    @pytest.mark.asyncio
    async def test_late_joiner_becomes_spectator(self):
        """Test that users joining active games become spectators."""
        # Create and start a game
        room = await room_manager.create_room(
            host=self.host_user,
            template_id=self.template.id
        )
        
        await room_manager.join_room(room.room_code, self.participant1)
        await room_manager.transition_room_state(
            room.room_code, RoomState.IN_PROGRESS, self.host_user
        )
        
        # Late joiner connects
        late_joiner = await database_sync_to_async(User.objects.create_user)(
            username='late_joiner',
            password='testpass123'
        )
        
        # Join via API
        late_client = self.get_authenticated_api_client(late_joiner)
        join_response = late_client.post('/api/rooms/join/', {
            'room_code': room.room_code
        })
        
        assert join_response.status_code == 200
        
        # Connect via WebSocket
        late_ws = await self.get_authenticated_websocket(late_joiner, room.room_code)
        await late_ws.connect()
        
        # Should receive room state indicating spectator status
        state_msg = await late_ws.receive_json_from()
        assert state_msg['type'] == 'room.state'
        
        # Verify in database that user is spectator
        room_obj = await room_manager.get_room(room.room_code)
        participants = await database_sync_to_async(list)(
            room_obj.room_participants.all()
        )
        
        late_participant = next(p for p in participants if p.user == late_joiner)
        assert late_participant.state == UserState.SPECTATING
        
        await late_ws.disconnect()
        await database_sync_to_async(late_joiner.delete)()
    
    @pytest.mark.asyncio
    async def test_host_transfer_during_game(self):
        """Test host transfer functionality during active game."""
        # Create and start a game
        room = await room_manager.create_room(
            host=self.host_user,
            template_id=self.template.id
        )
        
        await room_manager.join_room(room.room_code, self.participant1)
        await room_manager.join_room(room.room_code, self.participant2)
        await room_manager.transition_room_state(
            room.room_code, RoomState.IN_PROGRESS, self.host_user
        )
        
        # Connect all users
        host_ws = await self.get_authenticated_websocket(self.host_user, room.room_code)
        p1_ws = await self.get_authenticated_websocket(self.participant1, room.room_code)
        p2_ws = await self.get_authenticated_websocket(self.participant2, room.room_code)
        
        await host_ws.connect()
        await p1_ws.connect()
        await p2_ws.connect()
        
        # Clear initial messages
        await host_ws.receive_json_from()
        await p1_ws.receive_json_from()
        await p2_ws.receive_json_from()
        
        # Host leaves the room
        await room_manager.leave_room(room.room_code, self.host_user)
        
        # Participants should receive host transfer notification
        p1_msg = await p1_ws.receive_json_from()
        p2_msg = await p2_ws.receive_json_from()
        
        assert p1_msg['type'] == 'host.transferred' or p1_msg['type'] == 'room.state'
        assert p2_msg['type'] == 'host.transferred' or p2_msg['type'] == 'room.state'
        
        # Verify new host in database
        room_obj = await room_manager.get_room(room.room_code)
        assert room_obj.host in [self.participant1, self.participant2]
        assert room_obj.host != self.host_user
        
        await host_ws.disconnect()
        await p1_ws.disconnect()
        await p2_ws.disconnect()
