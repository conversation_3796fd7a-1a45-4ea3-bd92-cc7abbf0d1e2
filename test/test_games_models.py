"""
全面的游戏模型测试

测试games.models中的所有模型，包括：
- Game模型：游戏会话管理、类型验证
- PictionaryGame模型：你画我猜游戏状态、词汇管理
- PlayerScore模型：玩家得分管理、唯一性约束
"""

import pytest
from datetime import datetime, timedelta
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.utils import timezone

from games.models import Game, PictionaryGame, PlayerScore
from core.models import Room, RoomParticipant
from events.models import EventTemplate, EventStep

User = get_user_model()


class GameModelTest(TestCase):
    """测试Game模型的功能"""
    
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.template = EventTemplate.objects.create(
            name='Test Template',
            creator=self.user
        )
        
        self.room = Room.objects.create(
            room_code='TEST01',
            host=self.user,
            event_template=self.template
        )
    
    def test_game_creation_with_defaults(self):
        """测试游戏创建时的默认值"""
        game = Game.objects.create(
            room=self.room,
            game_type=Game.GAME_PICTIONARY
        )
        
        self.assertEqual(game.room, self.room)
        self.assertEqual(game.game_type, Game.GAME_PICTIONARY)
        self.assertTrue(game.is_active)
        self.assertIsNotNone(game.created_at)
    
    def test_game_types(self):
        """测试游戏类型"""
        # 测试你画我猜游戏
        pictionary_game = Game.objects.create(
            room=self.room,
            game_type=Game.GAME_PICTIONARY
        )
        self.assertEqual(pictionary_game.game_type, Game.GAME_PICTIONARY)
        self.assertEqual(pictionary_game.get_game_type_display(), 'Pictionary')
    
    def test_game_str_representation(self):
        """测试游戏字符串表示"""
        game = Game.objects.create(
            room=self.room,
            game_type=Game.GAME_PICTIONARY
        )
        
        expected = "Pictionary in Room TEST01"
        self.assertEqual(str(game), expected)
    
    def test_game_room_relationship(self):
        """测试游戏与房间的关系"""
        game = Game.objects.create(
            room=self.room,
            game_type=Game.GAME_PICTIONARY
        )
        
        # 测试一对一关系
        self.assertEqual(self.room.game, game)
        self.assertEqual(game.room, self.room)
    
    def test_game_active_status(self):
        """测试游戏活跃状态"""
        game = Game.objects.create(
            room=self.room,
            game_type=Game.GAME_PICTIONARY
        )
        
        # 测试默认为活跃状态
        self.assertTrue(game.is_active)
        
        # 测试设置为非活跃状态
        game.is_active = False
        game.save()
        self.assertFalse(game.is_active)


class PictionaryGameModelTest(TestCase):
    """测试PictionaryGame模型的功能"""
    
    def setUp(self):
        """设置测试数据"""
        self.host = User.objects.create_user(
            username='host',
            password='testpass123'
        )
        
        self.drawer = User.objects.create_user(
            username='drawer',
            password='testpass123'
        )
        
        self.template = EventTemplate.objects.create(
            name='Pictionary Template',
            creator=self.host
        )
        
        self.room = Room.objects.create(
            room_code='PICT01',
            host=self.host,
            event_template=self.template
        )
        
        self.game = Game.objects.create(
            room=self.room,
            game_type=Game.GAME_PICTIONARY
        )
    
    def test_pictionary_game_creation(self):
        """测试你画我猜游戏创建"""
        pictionary_game = PictionaryGame.objects.create(
            game=self.game,
            current_word='苹果',
            current_drawer=self.drawer
        )
        
        self.assertEqual(pictionary_game.game, self.game)
        self.assertEqual(pictionary_game.current_word, '苹果')
        self.assertEqual(pictionary_game.current_drawer, self.drawer)
        self.assertIsNotNone(pictionary_game.round_start_time)
    
    def test_pictionary_game_str_representation(self):
        """测试你画我猜游戏字符串表示"""
        pictionary_game = PictionaryGame.objects.create(
            game=self.game,
            current_word='香蕉',
            current_drawer=self.drawer
        )
        
        expected = "Pictionary for Pictionary in Room PICT01"
        self.assertEqual(str(pictionary_game), expected)
    
    def test_pictionary_game_word_management(self):
        """测试你画我猜游戏词汇管理"""
        words = ['苹果', '香蕉', '太阳', '月亮']
        
        for word in words:
            pictionary_game = PictionaryGame.objects.create(
                game=self.game,
                current_word=word,
                current_drawer=self.drawer
            )
            self.assertEqual(pictionary_game.current_word, word)
            # 清理以便下次创建
            pictionary_game.delete()
    
    def test_pictionary_game_drawer_management(self):
        """测试你画我猜游戏绘画者管理"""
        # 创建多个用户
        users = []
        for i in range(3):
            user = User.objects.create_user(
                username=f'player{i}',
                password='testpass123'
            )
            users.append(user)
        
        # 测试不同的绘画者
        for i, user in enumerate(users):
            pictionary_game = PictionaryGame.objects.create(
                game=self.game,
                current_word=f'word{i}',
                current_drawer=user
            )
            self.assertEqual(pictionary_game.current_drawer, user)
            # 清理以便下次创建
            pictionary_game.delete()
    
    def test_pictionary_game_round_time_tracking(self):
        """测试你画我猜游戏回合时间跟踪"""
        start_time = timezone.now()
        
        pictionary_game = PictionaryGame.objects.create(
            game=self.game,
            current_word='测试词汇',
            current_drawer=self.drawer
        )
        
        # 检查回合开始时间是否在合理范围内
        time_diff = abs((pictionary_game.round_start_time - start_time).total_seconds())
        self.assertLess(time_diff, 5)  # 应该在5秒内
    
    def test_pictionary_game_without_drawer(self):
        """测试没有绘画者的你画我猜游戏"""
        pictionary_game = PictionaryGame.objects.create(
            game=self.game,
            current_word='无绘画者',
            current_drawer=None
        )
        
        self.assertEqual(pictionary_game.current_word, '无绘画者')
        self.assertIsNone(pictionary_game.current_drawer)
    
    def test_pictionary_game_relationship_with_game(self):
        """测试你画我猜游戏与游戏的关系"""
        pictionary_game = PictionaryGame.objects.create(
            game=self.game,
            current_word='关系测试',
            current_drawer=self.drawer
        )
        
        # 测试一对一关系
        self.assertEqual(self.game.pictionary_data, pictionary_game)
        self.assertEqual(pictionary_game.game, self.game)


class PlayerScoreModelTest(TestCase):
    """测试PlayerScore模型的功能"""
    
    def setUp(self):
        """设置测试数据"""
        self.host = User.objects.create_user(
            username='host',
            password='testpass123'
        )
        
        self.player1 = User.objects.create_user(
            username='player1',
            password='testpass123'
        )
        
        self.player2 = User.objects.create_user(
            username='player2',
            password='testpass123'
        )
        
        self.template = EventTemplate.objects.create(
            name='Score Template',
            creator=self.host
        )
        
        self.room = Room.objects.create(
            room_code='SCORE01',
            host=self.host,
            event_template=self.template
        )
    
    def test_player_score_creation_with_defaults(self):
        """测试玩家得分创建时的默认值"""
        score = PlayerScore.objects.create(
            room=self.room,
            player=self.player1
        )
        
        self.assertEqual(score.room, self.room)
        self.assertEqual(score.player, self.player1)
        self.assertEqual(score.score, 0)
    
    def test_player_score_with_custom_score(self):
        """测试带自定义得分的玩家得分"""
        score = PlayerScore.objects.create(
            room=self.room,
            player=self.player1,
            score=100
        )
        
        self.assertEqual(score.score, 100)
    
    def test_player_score_str_representation(self):
        """测试玩家得分字符串表示"""
        score = PlayerScore.objects.create(
            room=self.room,
            player=self.player1,
            score=50
        )
        
        expected = "player1: 50 in Room SCORE01"
        self.assertEqual(str(score), expected)
    
    def test_player_score_unique_constraint(self):
        """测试玩家得分唯一性约束"""
        # 创建第一个得分记录
        PlayerScore.objects.create(
            room=self.room,
            player=self.player1,
            score=10
        )
        
        # 尝试为同一玩家在同一房间创建重复记录应该失败
        with self.assertRaises(IntegrityError):
            PlayerScore.objects.create(
                room=self.room,
                player=self.player1,
                score=20
            )
    
    def test_multiple_players_same_room(self):
        """测试同一房间多个玩家的得分"""
        score1 = PlayerScore.objects.create(
            room=self.room,
            player=self.player1,
            score=100
        )
        
        score2 = PlayerScore.objects.create(
            room=self.room,
            player=self.player2,
            score=150
        )
        
        self.assertEqual(score1.player, self.player1)
        self.assertEqual(score1.score, 100)
        self.assertEqual(score2.player, self.player2)
        self.assertEqual(score2.score, 150)
    
    def test_same_player_different_rooms(self):
        """测试同一玩家在不同房间的得分"""
        # 创建另一个房间
        room2 = Room.objects.create(
            room_code='SCORE02',
            host=self.host,
            event_template=self.template
        )
        
        score1 = PlayerScore.objects.create(
            room=self.room,
            player=self.player1,
            score=100
        )
        
        score2 = PlayerScore.objects.create(
            room=room2,
            player=self.player1,
            score=200
        )
        
        self.assertEqual(score1.room, self.room)
        self.assertEqual(score1.score, 100)
        self.assertEqual(score2.room, room2)
        self.assertEqual(score2.score, 200)
    
    def test_player_score_negative_values(self):
        """测试玩家得分负值"""
        score = PlayerScore.objects.create(
            room=self.room,
            player=self.player1,
            score=-10
        )
        
        self.assertEqual(score.score, -10)
    
    def test_player_score_large_values(self):
        """测试玩家得分大值"""
        large_score = 999999
        score = PlayerScore.objects.create(
            room=self.room,
            player=self.player1,
            score=large_score
        )
        
        self.assertEqual(score.score, large_score)
    
    def test_player_score_room_relationship(self):
        """测试玩家得分与房间的关系"""
        score1 = PlayerScore.objects.create(
            room=self.room,
            player=self.player1,
            score=50
        )
        
        score2 = PlayerScore.objects.create(
            room=self.room,
            player=self.player2,
            score=75
        )
        
        # 测试房间的得分关系
        room_scores = list(self.room.scores.all())
        self.assertIn(score1, room_scores)
        self.assertIn(score2, room_scores)
        self.assertEqual(len(room_scores), 2)
