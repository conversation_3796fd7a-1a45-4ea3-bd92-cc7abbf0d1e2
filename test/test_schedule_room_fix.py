import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from core.models import SystemTemplate, Room, RoomState
from events.models import EventTemplate, EventStep
from django.utils import timezone
from datetime import timedelta

User = get_user_model()


class TestScheduleRoomFix(TestCase):
    """测试预约房间功能修复"""

    def setUp(self):
        """设置测试数据"""
        self.client = APIClient()
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        # 创建JWT token
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        # 创建系统模板
        self.system_template = SystemTemplate.objects.create(
            name='测试系统模板',
            description='用于预约测试的系统模板',
            required_subscription=User.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {
                        'id': 'step_1',
                        'name': '你画我猜游戏',
                        'step_type': 'GAME_PICTIONARY',
                        'order': 1,
                        'duration': 1800,
                        'configuration': {'rounds': 5}
                    }
                ]
            }
        )
        
        # 创建用户模板
        self.user_template = EventTemplate.objects.create(
            name='测试用户模板',
            description='用于预约测试的用户模板',
            creator=self.user
        )
        
        # 为用户模板添加步骤
        EventStep.objects.create(
            template=self.user_template,
            name='自由讨论',
            step_type='FREE_CHAT',
            order=1,
            duration=600
        )

    def test_schedule_room_with_system_template(self):
        """测试使用系统模板预约房间"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        future_time = timezone.now() + timedelta(hours=2)
        
        schedule_data = {
            'name': '系统模板测试房间',
            'template_id': f'system_{self.system_template.id}',
            'scheduled_start_time': future_time.isoformat(),
            'duration_hours': 2
        }
        
        url = reverse('room-schedule')
        response = self.client.post(url, schedule_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证房间创建
        room = Room.objects.get(room_code=response.data['room_code'])
        self.assertEqual(room.status, RoomState.SCHEDULED)
        self.assertEqual(room.host, self.user)
        self.assertIsNone(room.event_template)  # 系统模板不关联EventTemplate

    def test_schedule_room_with_user_template(self):
        """测试使用用户模板预约房间"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        future_time = timezone.now() + timedelta(hours=2)
        
        schedule_data = {
            'name': '用户模板测试房间',
            'template_id': f'user_{self.user_template.id}',
            'scheduled_start_time': future_time.isoformat(),
            'duration_hours': 2
        }
        
        url = reverse('room-schedule')
        response = self.client.post(url, schedule_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证房间创建
        room = Room.objects.get(room_code=response.data['room_code'])
        self.assertEqual(room.status, RoomState.SCHEDULED)
        self.assertEqual(room.host, self.user)
        self.assertEqual(room.event_template, self.user_template)

    def test_schedule_room_with_legacy_template_id(self):
        """测试使用旧格式模板ID预约房间（向后兼容）"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        future_time = timezone.now() + timedelta(hours=2)
        
        schedule_data = {
            'name': '兼容性测试房间',
            'template_id': self.user_template.id,  # 旧格式：纯数字
            'scheduled_start_time': future_time.isoformat(),
            'duration_hours': 2
        }
        
        url = reverse('room-schedule')
        response = self.client.post(url, schedule_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证房间创建
        room = Room.objects.get(room_code=response.data['room_code'])
        self.assertEqual(room.event_template, self.user_template)

    def test_schedule_room_invalid_template(self):
        """测试使用无效模板ID预约房间"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        future_time = timezone.now() + timedelta(hours=2)
        
        schedule_data = {
            'name': '无效模板测试房间',
            'template_id': 'system_99999',  # 不存在的系统模板
            'scheduled_start_time': future_time.isoformat(),
            'duration_hours': 2
        }
        
        url = reverse('room-schedule')
        response = self.client.post(url, schedule_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('Template not found', response.data['error'])

    def test_schedule_room_access_denied_template(self):
        """测试访问无权限模板"""
        # 创建另一个用户的模板
        other_user = User.objects.create_user(
            username='otheruser',
            password='testpass'
        )
        other_template = EventTemplate.objects.create(
            name='其他用户模板',
            description='其他用户的模板',
            creator=other_user
        )
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        future_time = timezone.now() + timedelta(hours=2)
        
        schedule_data = {
            'name': '无权限模板测试房间',
            'template_id': f'user_{other_template.id}',
            'scheduled_start_time': future_time.isoformat(),
            'duration_hours': 2
        }
        
        url = reverse('room-schedule')
        response = self.client.post(url, schedule_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('Template not found', response.data['error'])
