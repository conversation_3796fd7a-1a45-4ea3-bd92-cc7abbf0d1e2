"""
测试ID字段格式验证
验证API端点正确处理无效的ID字段格式
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from events.models import EventTemplate, EventStep

User = get_user_model()


@pytest.mark.api
class TestIDFieldValidation(TestCase):
    """测试ID字段格式验证"""

    def setUp(self):
        """设置测试数据"""
        self.client = APIClient()
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # 创建测试模板
        self.template = EventTemplate.objects.create(
            name='Test Template',
            description='Test Description',
            creator=self.user
        )
        
        # 创建测试步骤
        self.step = EventStep.objects.create(
            template=self.template,
            name='Test Step',
            step_type=EventStep.STEP_FREE_CHAT,
            order=1,
            duration=300
        )

    def authenticate_user(self):
        """认证用户"""
        self.client.force_authenticate(user=self.user)

    def test_room_create_with_invalid_template_id_format(self):
        """测试房间创建时无效的template_id格式"""
        self.authenticate_user()
        
        invalid_template_ids = [
            'user_3',           # 字符串格式
            'template_abc',     # 包含字母
            '123.45',          # 浮点数
            'null',            # 字符串null
            '',                # 空字符串
            '  ',              # 空白字符
            '1e5',             # 科学计数法
            '0x10',            # 十六进制
        ]
        
        for invalid_id in invalid_template_ids:
            with self.subTest(template_id=invalid_id):
                data = {'template_id': invalid_id}
                response = self.client.post('/api/rooms/create/', data)
                
                # 应该返回400错误，说明ID格式无效
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
                self.assertIn('Template ID must be a valid integer', response.data.get('error', ''))

    def test_room_create_with_valid_template_id_format(self):
        """测试房间创建时有效的template_id格式"""
        self.authenticate_user()
        
        valid_template_ids = [
            str(self.template.id),  # 字符串格式的有效ID
            self.template.id,       # 整数格式的有效ID
        ]
        
        for valid_id in valid_template_ids:
            with self.subTest(template_id=valid_id):
                data = {'template_id': valid_id}
                response = self.client.post('/api/rooms/create/', data)
                
                # 应该成功创建房间
                self.assertEqual(response.status_code, status.HTTP_201_CREATED)
                self.assertIn('room_code', response.data)

    def test_schedule_room_with_invalid_template_id_format(self):
        """测试预约房间时无效的template_id格式"""
        self.authenticate_user()
        
        invalid_template_ids = [
            'user_3',
            'template_abc',
            '123.45',
        ]
        
        for invalid_id in invalid_template_ids:
            with self.subTest(template_id=invalid_id):
                data = {
                    'name': 'Test Scheduled Room',
                    'template_id': invalid_id,
                    'scheduled_start_time': '2025-12-25T14:30:00Z',
                    'duration_hours': 2
                }
                response = self.client.post('/api/rooms/schedule/', data)
                
                # 应该返回400错误，说明ID格式无效
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
                self.assertIn('Template ID must be a valid integer', response.data.get('error', ''))

    def test_reorder_steps_with_invalid_step_ids_format(self):
        """测试重排步骤时无效的step_ids格式"""
        self.authenticate_user()
        
        invalid_step_ids_lists = [
            ['step_1', 'step_2'],      # 字符串格式
            ['1.5', '2.5'],           # 浮点数格式
            ['abc', 'def'],           # 纯字母
            [None, 'null'],           # 包含None
            ['', '  '],               # 空字符串
        ]
        
        for invalid_ids in invalid_step_ids_lists:
            with self.subTest(step_ids=invalid_ids):
                data = {'step_ids': invalid_ids}
                response = self.client.post(f'/api/events/templates/{self.template.id}/reorder-steps/', data)
                
                # 应该返回400错误，说明step_ids格式无效
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
                self.assertIn('All step_ids must be valid integers', response.data.get('error', ''))

    def test_reorder_steps_with_valid_step_ids_format(self):
        """测试重排步骤时有效的step_ids格式"""
        self.authenticate_user()

        # 创建第二个步骤
        step2 = EventStep.objects.create(
            template=self.template,
            name='Test Step 2',
            step_type=EventStep.STEP_FREE_CHAT,
            order=2,
            duration=300
        )

        # 测试字符串格式的有效ID - 只验证格式验证通过，不要求功能完全正确
        data = {'step_ids': [str(self.step.id), str(step2.id)]}
        response = self.client.post(f'/api/events/templates/{self.template.id}/reorder-steps/', data)
        # 只要不是格式错误就算通过
        self.assertNotEqual(response.status_code, status.HTTP_400_BAD_REQUEST,
                           "Should not fail due to ID format issues")

    def test_edge_cases_for_id_validation(self):
        """测试ID验证的边界情况"""
        self.authenticate_user()
        
        edge_cases = [
            ('0', True),           # 零值（虽然不存在，但格式有效）
            ('-1', True),          # 负数（格式有效）
            ('999999', True),      # 大数值（格式有效）
            ('01', True),          # 前导零（格式有效）
            ('+123', True),        # 正号（格式有效）
            ('123abc', False),     # 数字+字母（格式无效）
            ('abc123', False),     # 字母+数字（格式无效）
            ('12.0', False),       # 小数点（格式无效）
            ('1,234', False),      # 逗号分隔（格式无效）
        ]
        
        for test_id, should_be_valid in edge_cases:
            with self.subTest(template_id=test_id, should_be_valid=should_be_valid):
                data = {'template_id': test_id}
                response = self.client.post('/api/rooms/create/', data)
                
                if should_be_valid:
                    # 格式有效，但可能因为ID不存在而返回404
                    self.assertIn(response.status_code, [status.HTTP_404_NOT_FOUND, status.HTTP_201_CREATED])
                else:
                    # 格式无效，应该返回400
                    self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
                    self.assertIn('Template ID must be a valid integer', response.data.get('error', ''))

    def test_mixed_valid_invalid_step_ids(self):
        """测试混合有效和无效step_ids的情况"""
        self.authenticate_user()
        
        mixed_cases = [
            [self.step.id, 'invalid'],     # 一个有效，一个无效
            ['invalid', self.step.id],     # 一个无效，一个有效
            [self.step.id, '123.45'],      # 整数和浮点数
            ['valid_format', 'invalid'],   # 两个都无效
        ]
        
        for mixed_ids in mixed_cases:
            with self.subTest(step_ids=mixed_ids):
                data = {'step_ids': mixed_ids}
                response = self.client.post(f'/api/events/templates/{self.template.id}/reorder-steps/', data)
                
                # 只要有一个无效，整个请求就应该失败
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
                self.assertIn('All step_ids must be valid integers', response.data.get('error', ''))
