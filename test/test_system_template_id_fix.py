#!/usr/bin/env python3
"""
测试系统模板ID格式修复的专门测试
"""

import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from core.models import SystemTemplate, TemplateManager
from events.models import EventTemplate

User = get_user_model()


class TestSystemTemplateIDFix(TestCase):
    """测试系统模板ID格式修复"""

    def setUp(self):
        """设置测试数据"""
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level='FREE'
        )
        
        # 创建API客户端
        self.client = APIClient()
        
        # 获取JWT token
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        # 确保有系统模板存在
        self.system_template = SystemTemplate.objects.filter(is_active=True).first()
        if not self.system_template:
            self.system_template = SystemTemplate.objects.create(
                name='测试系统模板',
                description='用于测试的系统模板',
                required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
                template_config={
                    'steps': [
                        {'type': 'FREE_CHAT', 'duration': 300}
                    ]
                }
            )

    def test_system_template_id_format_in_list(self):
        """测试模板列表API返回正确的系统模板ID格式"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        response = self.client.get('/api/room-templates/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        templates = response.data['templates']
        
        # 查找系统模板
        system_templates = [t for t in templates if t['type'] == 'system']
        self.assertGreater(len(system_templates), 0)
        
        # 验证ID格式
        for template in system_templates:
            self.assertTrue(template['id'].startswith('system_'))
            self.assertIsInstance(template['id'], str)

    def test_create_room_with_system_template_id(self):
        """测试使用系统模板ID创建房间"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        # 获取系统模板ID
        system_template_id = f'system_{self.system_template.id}'
        
        # 创建房间
        response = self.client.post('/api/rooms/create/', {
            'template_id': system_template_id
        })
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('room_code', response.data)
        self.assertEqual(response.data['host'], self.user.username)
        self.assertEqual(response.data['status'], 'OPEN')
        
        # 验证房间的event_template字段为None（系统模板）
        self.assertIsNone(response.data['event_template'])

    def test_create_room_with_user_template_id(self):
        """测试使用用户模板ID创建房间（确保向后兼容）"""
        # 创建用户模板
        user_template = EventTemplate.objects.create(
            name='用户测试模板',
            description='用于测试的用户模板',
            creator=self.user
        )
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        # 测试新格式ID
        user_template_id = f'user_{user_template.id}'
        response = self.client.post('/api/rooms/create/', {
            'template_id': user_template_id
        })
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('room_code', response.data)
        
        # 测试兼容旧格式ID（纯数字）
        response = self.client.post('/api/rooms/create/', {
            'template_id': user_template.id
        })
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('room_code', response.data)

    def test_template_manager_get_template_by_id(self):
        """测试TemplateManager.get_template_by_id方法"""
        # 测试系统模板
        system_template_id = f'system_{self.system_template.id}'
        template_obj, template_type = TemplateManager.get_template_by_id(system_template_id, self.user)
        
        self.assertIsNotNone(template_obj)
        self.assertEqual(template_type, 'system')
        self.assertEqual(template_obj.id, self.system_template.id)
        
        # 测试用户模板
        user_template = EventTemplate.objects.create(
            name='用户测试模板',
            description='用于测试的用户模板',
            creator=self.user
        )
        
        user_template_id = f'user_{user_template.id}'
        template_obj, template_type = TemplateManager.get_template_by_id(user_template_id, self.user)
        
        self.assertIsNotNone(template_obj)
        self.assertEqual(template_type, 'user')
        self.assertEqual(template_obj.id, user_template.id)
        
        # 测试兼容旧格式
        template_obj, template_type = TemplateManager.get_template_by_id(user_template.id, self.user)
        
        self.assertIsNotNone(template_obj)
        self.assertEqual(template_type, 'user')
        self.assertEqual(template_obj.id, user_template.id)

    def test_invalid_template_id_format(self):
        """测试无效的模板ID格式"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        invalid_ids = [
            'invalid_format',
            'system_abc',
            'user_xyz',
            'system_999999',  # 不存在的系统模板
            'user_999999',    # 不存在的用户模板
        ]
        
        for invalid_id in invalid_ids:
            response = self.client.post('/api/rooms/create/', {
                'template_id': invalid_id
            })
            
            # 应该返回404或400错误
            self.assertIn(response.status_code, [
                status.HTTP_400_BAD_REQUEST,
                status.HTTP_404_NOT_FOUND
            ], f"Invalid ID {invalid_id} should return error")

    def test_access_control_for_system_templates(self):
        """测试系统模板的访问控制"""
        # 创建需要Pro订阅的系统模板
        pro_template = SystemTemplate.objects.create(
            name='Pro专用模板',
            description='需要Pro订阅的模板',
            required_subscription=SystemTemplate.SUBSCRIPTION_PRO,
            template_config={
                'steps': [
                    {'type': 'GAME_PICTIONARY', 'duration': 600}
                ]
            }
        )

        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')

        # 免费用户尝试使用Pro模板
        pro_template_id = f'system_{pro_template.id}'
        response = self.client.post('/api/rooms/create/', {
            'template_id': pro_template_id
        })

        # 免费用户无法访问Pro模板，应该返回404（模板不存在或无权限）
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('Template not found or access denied', response.data['error'])
