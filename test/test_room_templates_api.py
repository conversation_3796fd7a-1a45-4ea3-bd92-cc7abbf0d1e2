import pytest
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from core.models import SystemTemplate

User = get_user_model()


class TestRoomTemplatesAPI(TestCase):
    """测试房间模板API"""

    def setUp(self):
        """设置测试数据"""
        self.client = APIClient()
        
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        # 创建JWT token
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        # 创建系统模板
        self.system_template = SystemTemplate.objects.create(
            name='测试系统模板',
            description='用于API测试的系统模板',
            required_subscription=User.SUBSCRIPTION_FREE,
            template_config={'steps': []}
        )

    def test_room_templates_api_authenticated(self):
        """测试认证用户可以访问房间模板API"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        url = reverse('room-templates')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('templates', response.data)
        self.assertIsInstance(response.data['templates'], list)

    def test_room_templates_api_unauthenticated(self):
        """测试未认证用户无法访问房间模板API"""
        url = reverse('room-templates')
        response = self.client.get(url)

        # 接受401或403状态码，都表示未授权访问
        self.assertIn(response.status_code, [status.HTTP_401_UNAUTHORIZED, status.HTTP_403_FORBIDDEN])

    def test_room_templates_includes_system_templates(self):
        """测试房间模板API包含系统模板"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        url = reverse('room-templates')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        templates = response.data['templates']
        system_templates = [t for t in templates if t['type'] == 'system']
        
        self.assertGreater(len(system_templates), 0)
        
        # 检查系统模板格式
        for template in system_templates:
            self.assertTrue(template['id'].startswith('system_'))
            self.assertEqual(template['creator_username'], 'System')
            self.assertIn('required_subscription', template)

    def test_room_templates_response_format(self):
        """测试房间模板API响应格式"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        url = reverse('room-templates')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 检查响应结构
        self.assertIn('templates', response.data)
        self.assertIn('total_count', response.data)
        
        templates = response.data['templates']
        if templates:
            template = templates[0]
            required_fields = ['id', 'name', 'description', 'type', 'creator_username', 'steps']
            for field in required_fields:
                self.assertIn(field, template)

    def test_system_templates_appear_first(self):
        """测试系统模板出现在用户模板之前"""
        # 创建用户模板
        from events.models import EventTemplate
        user_template = EventTemplate.objects.create(
            name='用户模板',
            description='测试用户模板',
            creator=self.user
        )
        
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        url = reverse('room-templates')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        templates = response.data['templates']
        
        # 找到第一个系统模板和第一个用户模板的位置
        first_system_index = None
        first_user_index = None
        
        for i, template in enumerate(templates):
            if template['type'] == 'system' and first_system_index is None:
                first_system_index = i
            elif template['type'] == 'user' and first_user_index is None:
                first_user_index = i
        
        # 如果两种模板都存在，系统模板应该在前面
        if first_system_index is not None and first_user_index is not None:
            self.assertLess(first_system_index, first_user_index)
