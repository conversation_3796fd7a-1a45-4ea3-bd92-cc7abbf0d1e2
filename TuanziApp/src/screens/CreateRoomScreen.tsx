import React, { useState, useCallback } from 'react';
import { View, Text, FlatList, Alert, ActivityIndicator, TouchableOpacity, StyleSheet } from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
// --- FIX: Correct the import path ---
// The correct path from 'src/screens/' to 'src/auth/' is one level up ('../').
import { useAuth } from '../auth/AuthContext';
import { getRoomTemplates } from '../api/calendarApi';
import { API_URL } from '../api/client';
import { RootStackParamList } from '../types';
import { commonStyles } from '../styles/commonStyles';
import { theme } from '../styles/theme';

type NavigationProp = StackNavigationProp<RootStackParamList, 'CreateRoom'>;

interface Template {
  id: string;
  name: string;
  description: string;
  type: 'system' | 'user';
  creator_username: string;
  steps: any[];
}

export const CreateRoomScreen = () => {
    const { token } = useAuth();
    const navigation = useNavigation<NavigationProp>();
    const [templates, setTemplates] = useState<Template[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const fetchTemplates = useCallback(async () => {
        try {
            setIsLoading(true);
            const data = await getRoomTemplates();
            setTemplates(data);
        } catch (error) {
            Alert.alert('错误', '无法加载模板列表。');
        } finally {
            setIsLoading(false);
        }
    }, []);

    useFocusEffect(useCallback(() => { fetchTemplates(); }, [fetchTemplates]));

    const handleSelectTemplate = async (templateId: string) => {
        if (!token) return;
        try {
            const response = await fetch(`${API_URL}/api/rooms/create/`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
                body: JSON.stringify({ template_id: templateId }),
            });
            const data = await response.json();
            if (response.ok) {
                navigation.replace('Room', { room: data });
            } else {
                Alert.alert('错误', '无法创建房间。');
            }
        } catch (error) {
            Alert.alert('错误', '创建房间时发生错误。');
        }
    };

    const renderItem = ({ item }: { item: Template }) => (
        <TouchableOpacity style={styles.templateCard} onPress={() => handleSelectTemplate(item.id)}>
            <View style={styles.templateHeader}>
                <Text style={styles.templateName}>
                    {item.type === 'system' ? '🏛️' : '👤'} {item.name}
                </Text>
                <Text style={styles.templateType}>
                    {item.type === 'system' ? '系统模板' : '我的模板'}
                </Text>
            </View>
            <Text style={styles.templateDescription}>{item.description}</Text>
            {item.steps && item.steps.length > 0 && (
                <Text style={styles.stepsCount}>
                    包含 {item.steps.length} 个环节
                </Text>
            )}
        </TouchableOpacity>
    );

    if (isLoading) {
        return <View style={commonStyles.container}><ActivityIndicator size="large" /></View>;
    }

    // 分离系统模板和用户模板
    const systemTemplates = templates.filter(t => t.type === 'system');
    const userTemplates = templates.filter(t => t.type === 'user');

    // 合并模板列表，系统模板在前
    const sortedTemplates = [...systemTemplates, ...userTemplates];

    return (
        <View style={styles.container}>
            <FlatList
                data={sortedTemplates}
                renderItem={renderItem}
                keyExtractor={(item) => item.id}
                ListHeaderComponent={<Text style={commonStyles.title}>选择一个活动流程</Text>}
                contentContainerStyle={styles.listContainer}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background,
    },
    listContainer: {
        padding: 16,
    },
    templateCard: {
        backgroundColor: theme.colors.surface,
        padding: 20,
        marginVertical: 8,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: theme.colors.border,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    templateHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    templateName: {
        fontSize: 18,
        fontWeight: 'bold',
        color: theme.colors.text,
        flex: 1,
    },
    templateType: {
        fontSize: 12,
        color: theme.colors.primary,
        backgroundColor: theme.colors.background,
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 4,
        fontWeight: 'bold',
    },
    templateDescription: {
        fontSize: 14,
        color: theme.colors.textSecondary,
        marginTop: 5,
        lineHeight: 20,
    },
    stepsCount: {
        fontSize: 12,
        color: theme.colors.primary,
        marginTop: 8,
        fontWeight: '500',
    },
});
