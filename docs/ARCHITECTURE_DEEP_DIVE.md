# 团子项目架构深度解析

> **目标**: 为Augment提供项目架构的深度理解，包括数据流、状态管理、模块依赖等

## 🏛️ 整体架构

### 分层架构
```
┌─────────────────────────────────────────┐
│              前端层 (React Native)        │
├─────────────────────────────────────────┤
│              API层 (Django REST)         │
├─────────────────────────────────────────┤
│            业务逻辑层 (Services)          │
├─────────────────────────────────────────┤
│             数据层 (Models)              │
├─────────────────────────────────────────┤
│           实时通信层 (WebSocket)          │
└─────────────────────────────────────────┘
```

### 核心数据流
1. **HTTP请求流**: 前端 → API视图 → 业务服务 → 数据模型
2. **WebSocket流**: 前端 ↔ Consumer → 房间管理器 → 数据模型
3. **状态同步流**: 数据变更 → 信号 → WebSocket广播 → 前端更新

## 📊 数据模型关系

### 核心实体关系图
```
User (用户)
├── hosted_rooms (1:N) → Room (房间)
├── room_participants (1:N) → RoomParticipant (参与关系)
└── created_templates (1:N) → EventTemplate (模板)

Room (房间)
├── host (N:1) → User
├── room_participants (1:N) → RoomParticipant
├── event_template (N:1) → EventTemplate
└── games (1:N) → Game

EventTemplate (事件模板)
├── creator (N:1) → User
├── steps (1:N) → EventStep
└── rooms (1:N) → Room

RoomParticipant (参与关系)
├── user (N:1) → User
├── room (N:1) → Room
└── scores (1:N) → PlayerScore
```

### 关键约束和规则
1. **房间唯一性**: room_code全局唯一
2. **参与者唯一性**: (room, user)组合唯一
3. **模板权限**: 用户只能使用自己创建的模板或系统模板
4. **订阅限制**: 根据用户订阅等级限制功能访问

## 🔄 状态机设计

### 房间状态机
```
SCHEDULED (已预约)
    ↓ (到达预定时间)
OPEN (已开启)
    ↓ (有用户加入)
WAITING_FOR_HOST (等待房主) / READY (准备就绪)
    ↓ (房主开始游戏)
IN_PROGRESS (进行中)
    ↓ (游戏结束)
ENDED (已结束)
    ↓ (清理或超时)
CLOSED (已关闭)
```

### 用户状态机
```
JOINED (已加入)
    ↓ (准备游戏)
READY (准备就绪)
    ↓ (游戏开始)
PLAYING (游戏中) / SPECTATING (观战中)
```

### 状态转换规则
- **房间状态**: 只能按顺序转换，不可逆转
- **用户状态**: 可在JOINED和READY间切换
- **异常处理**: 支持紧急关闭和状态重置

## 🔧 核心服务组件

### 1. 房间管理器 (`core/services/room_manager.py`)
**职责**:
- 房间生命周期管理
- 参与者加入/离开处理
- 状态转换控制
- 容量和权限检查

**关键方法**:
```python
create_room_sync(host, template_id)     # 创建房间
join_room_sync(user, room_code)         # 加入房间
leave_room_sync(user, room_code)        # 离开房间
transition_room_state(room, new_state)  # 状态转换
```

### 2. 模板管理器 (`core/models.py`)
**职责**:
- 模板访问权限控制
- 系统模板和用户模板管理
- 订阅等级验证

**关键方法**:
```python
get_template_by_id(template_id, user)   # 获取模板
get_accessible_templates(user)          # 获取可访问模板
is_accessible_by_user(user)             # 权限检查
```

### 3. WebSocket消费者 (`core/consumers.py`)
**职责**:
- WebSocket连接管理
- 实时消息处理
- 房间广播控制
- 用户在线状态维护

**关键方法**:
```python
connect()                               # 连接处理
disconnect()                            # 断开处理
receive()                               # 消息接收
broadcast_to_room()                     # 房间广播
```

## 🔐 权限和安全

### 订阅等级控制
```python
# 订阅等级定义
SUBSCRIPTION_FREE = 'free'      # 免费用户
SUBSCRIPTION_PRO = 'pro'        # 专业用户  
SUBSCRIPTION_MAX = 'max'        # 最高级用户

# 功能限制
Free用户: 10人房间, 2小时时长, 基础步骤类型
Pro用户:  500人房间, 24小时时长, 高级步骤类型
Max用户:  2000人房间, 72小时时长, 所有功能
```

### 权限验证流程
1. **API层验证**: 检查JWT token有效性
2. **业务层验证**: 检查用户订阅等级
3. **数据层验证**: 检查资源访问权限
4. **实时层验证**: 检查WebSocket连接权限

## 📡 实时通信架构

### WebSocket连接管理
```python
# 连接URL格式
ws://host/ws/room/{room_code}/?token={jwt_token}

# 消息格式
{
    "type": "message_type",
    "payload": {
        "data": "message_data"
    }
}
```

### 消息类型定义
- **系统消息**: `user_joined`, `user_left`, `room_state_changed`
- **游戏消息**: `game_started`, `turn_changed`, `score_updated`
- **交互消息**: `chat_message`, `drawing_data`, `vote_cast`

### 广播策略
- **房间广播**: 向房间内所有连接的用户发送
- **角色广播**: 向特定角色用户发送（如仅房主）
- **个人消息**: 向特定用户发送私人消息

## 🧪 测试架构

### 测试分层
1. **单元测试**: 测试单个模型/方法
2. **集成测试**: 测试模块间交互
3. **API测试**: 测试HTTP接口
4. **WebSocket测试**: 测试实时通信
5. **端到端测试**: 测试完整用户流程

### 测试数据管理
- **测试隔离**: 每个测试使用独立数据库
- **数据工厂**: 使用工厂模式创建测试数据
- **Mock策略**: 对外部依赖进行Mock
- **异步测试**: 使用pytest-asyncio处理异步代码

## 🔄 数据流详解

### 房间创建流程
```
1. 前端发送POST /api/rooms/create/ {template_id}
2. RoomCreateView验证用户认证和模板权限
3. 调用room_manager.create_room_sync()
4. 创建Room和RoomParticipant记录
5. 返回房间信息给前端
6. 前端建立WebSocket连接
```

### 用户加入流程
```
1. 前端发送POST /api/rooms/join/ {room_code}
2. JoinRoomView验证房间状态和容量
3. 调用room_manager.join_room_sync()
4. 创建RoomParticipant记录
5. 通过WebSocket广播用户加入消息
6. 更新所有客户端的房间状态
```

### 游戏进行流程
```
1. 房主通过WebSocket发送开始游戏消息
2. RoomConsumer处理消息并验证权限
3. 调用对应的游戏处理器
4. 更新游戏状态和房间状态
5. 广播游戏开始消息给所有参与者
6. 处理游戏中的实时交互
```

## 🚀 性能优化

### 数据库优化
- **索引策略**: 为常用查询字段添加索引
- **查询优化**: 使用select_related和prefetch_related
- **连接池**: 配置数据库连接池
- **缓存策略**: 对频繁访问的数据进行缓存

### WebSocket优化
- **连接管理**: 及时清理断开的连接
- **消息队列**: 使用Redis作为消息代理
- **广播优化**: 批量发送消息减少网络开销
- **心跳检测**: 定期检测连接状态

### 内存管理
- **对象生命周期**: 及时释放不需要的对象
- **缓存控制**: 控制缓存大小和过期时间
- **垃圾回收**: 优化Python垃圾回收机制

## 📈 扩展性设计

### 水平扩展
- **无状态设计**: API服务器无状态，支持负载均衡
- **数据库分片**: 支持数据库水平分片
- **缓存分布**: 使用分布式缓存系统

### 功能扩展
- **插件架构**: 支持新游戏类型的插件化开发
- **事件系统**: 基于事件的松耦合架构
- **配置管理**: 支持动态配置更新

## 🔗 相关文档

- [Augment项目指南](./AUGMENT_PROJECT_GUIDE.md) - 快速开发指南
- [API参考文档](./API_REFERENCE.md) - 详细API文档
- [WebSocket通信协议](./WEBSOCKET_PROTOCOL.md) - 实时通信协议
- [数据库设计文档](./DATABASE_DESIGN.md) - 数据模型设计

---

**最后更新**: 2025-07-14
**维护者**: Augment AI Assistant
**版本**: 1.0
