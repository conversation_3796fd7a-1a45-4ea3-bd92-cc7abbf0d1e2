# 数据库设计文档

> **目标**: 为Augment提供完整的数据库设计文档，包括表结构、关系、索引、约束等

## 📊 数据库概览

### 技术选型
- **生产环境**: PostgreSQL 13+
- **开发环境**: SQLite 3
- **ORM**: Django ORM
- **迁移管理**: Django Migrations

### 数据库结构
```
团子数据库
├── 用户管理 (core app)
│   ├── auth_user (Django内置)
│   ├── core_user (扩展用户信息)
│   └── core_roomparticipant (参与关系)
├── 房间管理 (core app)
│   └── core_room (房间信息)
├── 事件模板 (events app)
│   ├── events_eventtemplate (事件模板)
│   └── events_eventstep (事件步骤)
├── 游戏数据 (games app)
│   ├── games_game (游戏基类)
│   ├── games_pictionarygame (你画我猜)
│   └── games_playerscore (玩家得分)
└── 系统模板 (core app)
    └── core_systemtemplate (系统模板)
```

## 👤 用户相关表

### core_user (用户扩展表)
```sql
CREATE TABLE core_user (
    id SERIAL PRIMARY KEY,
    user_ptr_id INTEGER UNIQUE REFERENCES auth_user(id),
    subscription_level VARCHAR(10) DEFAULT 'free',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_user_subscription ON core_user(subscription_level);
CREATE INDEX idx_user_created ON core_user(created_at);
```

**字段说明**:
- `user_ptr_id`: 指向Django内置用户表的外键
- `subscription_level`: 订阅等级 (free/pro/max)
- `created_at/updated_at`: 时间戳字段

### core_roomparticipant (房间参与关系)
```sql
CREATE TABLE core_roomparticipant (
    id SERIAL PRIMARY KEY,
    room_id INTEGER REFERENCES core_room(id),
    user_id INTEGER REFERENCES auth_user(id),
    role VARCHAR(20) DEFAULT 'PARTICIPANT',
    score INTEGER DEFAULT 0,
    state VARCHAR(20) DEFAULT 'JOINED',
    is_active BOOLEAN DEFAULT TRUE,
    joined_at TIMESTAMP DEFAULT NOW(),
    left_at TIMESTAMP NULL,
    last_active_at TIMESTAMP DEFAULT NOW(),
    custom_data JSONB DEFAULT '{}',
    
    UNIQUE(room_id, user_id)
);

-- 索引
CREATE INDEX idx_participant_room_active ON core_roomparticipant(room_id, is_active);
CREATE INDEX idx_participant_user_active ON core_roomparticipant(user_id, is_active);
CREATE INDEX idx_participant_role ON core_roomparticipant(role, is_active);
CREATE INDEX idx_participant_last_active ON core_roomparticipant(last_active_at);
```

**字段说明**:
- `role`: 用户角色 (HOST/ADMIN/PARTICIPANT)
- `score`: 用户在房间中的总得分
- `state`: 用户状态 (JOINED/READY/PLAYING/SPECTATING)
- `is_active`: 是否仍在房间中
- `custom_data`: JSON字段，存储扩展数据

## 🏠 房间相关表

### core_room (房间表)
```sql
CREATE TABLE core_room (
    id SERIAL PRIMARY KEY,
    room_code VARCHAR(10) UNIQUE NOT NULL,
    host_id INTEGER REFERENCES auth_user(id),
    event_template_id INTEGER REFERENCES events_eventtemplate(id),
    status VARCHAR(20) DEFAULT 'OPEN',
    created_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP NULL,
    closed_at TIMESTAMP NULL,
    review_started_at TIMESTAMP NULL,
    last_activity_at TIMESTAMP DEFAULT NOW(),
    scheduled_start_time TIMESTAMP NULL,
    max_participants INTEGER DEFAULT 10,
    duration_hours INTEGER DEFAULT 2,
    
    CHECK (max_participants > 0),
    CHECK (duration_hours > 0)
);

-- 索引
CREATE UNIQUE INDEX idx_room_code ON core_room(room_code);
CREATE INDEX idx_room_host ON core_room(host_id);
CREATE INDEX idx_room_status ON core_room(status);
CREATE INDEX idx_room_template ON core_room(event_template_id);
CREATE INDEX idx_room_scheduled ON core_room(scheduled_start_time);
CREATE INDEX idx_room_expires ON core_room(expires_at);
CREATE INDEX idx_room_activity ON core_room(last_activity_at);
```

**字段说明**:
- `room_code`: 6位房间代码，全局唯一
- `status`: 房间状态 (SCHEDULED/OPEN/READY/IN_PROGRESS/ENDED/CLOSED)
- `expires_at`: 房间过期时间
- `scheduled_start_time`: 预约开始时间
- `max_participants/duration_hours`: 订阅限制字段

## 📋 事件模板相关表

### events_eventtemplate (事件模板表)
```sql
CREATE TABLE events_eventtemplate (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    creator_id INTEGER REFERENCES auth_user(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- 索引
CREATE INDEX idx_template_creator ON events_eventtemplate(creator_id);
CREATE INDEX idx_template_active ON events_eventtemplate(is_active);
CREATE INDEX idx_template_created ON events_eventtemplate(created_at);
```

### events_eventstep (事件步骤表)
```sql
CREATE TABLE events_eventstep (
    id SERIAL PRIMARY KEY,
    template_id INTEGER REFERENCES events_eventtemplate(id),
    name VARCHAR(100) NOT NULL,
    order_num INTEGER NOT NULL,
    step_type VARCHAR(50) NOT NULL,
    duration INTEGER DEFAULT 300,
    configuration JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(template_id, order_num),
    CHECK (order_num > 0),
    CHECK (duration > 0)
);

-- 索引
CREATE INDEX idx_step_template_order ON events_eventstep(template_id, order_num);
CREATE INDEX idx_step_type ON events_eventstep(step_type);
```

**字段说明**:
- `order_num`: 步骤在模板中的顺序
- `step_type`: 步骤类型 (FREE_CHAT/GAME_PICTIONARY等)
- `configuration`: JSON配置字段

## 🎮 游戏相关表

### games_game (游戏基类表)
```sql
CREATE TABLE games_game (
    id SERIAL PRIMARY KEY,
    room_id INTEGER REFERENCES core_room(id),
    game_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'WAITING',
    current_round INTEGER DEFAULT 1,
    total_rounds INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    started_at TIMESTAMP NULL,
    ended_at TIMESTAMP NULL,
    game_data JSONB DEFAULT '{}'
);

-- 索引
CREATE INDEX idx_game_room ON games_game(room_id);
CREATE INDEX idx_game_type ON games_game(game_type);
CREATE INDEX idx_game_status ON games_game(status);
```

### games_pictionarygame (你画我猜游戏表)
```sql
CREATE TABLE games_pictionarygame (
    game_ptr_id INTEGER PRIMARY KEY REFERENCES games_game(id),
    current_drawer_id INTEGER REFERENCES auth_user(id),
    current_word VARCHAR(100),
    words_used JSONB DEFAULT '[]',
    round_time_limit INTEGER DEFAULT 300,
    drawing_data JSONB DEFAULT '{}'
);

-- 索引
CREATE INDEX idx_pictionary_drawer ON games_pictionarygame(current_drawer_id);
```

### games_playerscore (玩家得分表)
```sql
CREATE TABLE games_playerscore (
    id SERIAL PRIMARY KEY,
    game_id INTEGER REFERENCES games_game(id),
    player_id INTEGER REFERENCES auth_user(id),
    score INTEGER DEFAULT 0,
    round_scores JSONB DEFAULT '[]',
    achievements JSONB DEFAULT '[]',
    
    UNIQUE(game_id, player_id)
);

-- 索引
CREATE INDEX idx_score_game_player ON games_playerscore(game_id, player_id);
CREATE INDEX idx_score_value ON games_playerscore(score);
```

## 🏛️ 系统模板表

### core_systemtemplate (系统模板表)
```sql
CREATE TABLE core_systemtemplate (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    template_data JSONB NOT NULL,
    required_subscription VARCHAR(10) DEFAULT 'free',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_system_template_subscription ON core_systemtemplate(required_subscription);
CREATE INDEX idx_system_template_active ON core_systemtemplate(is_active);
```

## 🔗 关系约束

### 外键关系
```sql
-- 房间关系
ALTER TABLE core_room ADD CONSTRAINT fk_room_host 
    FOREIGN KEY (host_id) REFERENCES auth_user(id);
ALTER TABLE core_room ADD CONSTRAINT fk_room_template 
    FOREIGN KEY (event_template_id) REFERENCES events_eventtemplate(id);

-- 参与者关系
ALTER TABLE core_roomparticipant ADD CONSTRAINT fk_participant_room 
    FOREIGN KEY (room_id) REFERENCES core_room(id) ON DELETE CASCADE;
ALTER TABLE core_roomparticipant ADD CONSTRAINT fk_participant_user 
    FOREIGN KEY (user_id) REFERENCES auth_user(id);

-- 模板关系
ALTER TABLE events_eventtemplate ADD CONSTRAINT fk_template_creator 
    FOREIGN KEY (creator_id) REFERENCES auth_user(id);
ALTER TABLE events_eventstep ADD CONSTRAINT fk_step_template 
    FOREIGN KEY (template_id) REFERENCES events_eventtemplate(id) ON DELETE CASCADE;

-- 游戏关系
ALTER TABLE games_game ADD CONSTRAINT fk_game_room 
    FOREIGN KEY (room_id) REFERENCES core_room(id);
ALTER TABLE games_playerscore ADD CONSTRAINT fk_score_game 
    FOREIGN KEY (game_id) REFERENCES games_game(id) ON DELETE CASCADE;
```

### 唯一约束
```sql
-- 房间代码全局唯一
ALTER TABLE core_room ADD CONSTRAINT uk_room_code UNIQUE (room_code);

-- 用户在房间中唯一
ALTER TABLE core_roomparticipant ADD CONSTRAINT uk_room_user UNIQUE (room_id, user_id);

-- 模板步骤顺序唯一
ALTER TABLE events_eventstep ADD CONSTRAINT uk_template_order UNIQUE (template_id, order_num);

-- 游戏中玩家得分唯一
ALTER TABLE games_playerscore ADD CONSTRAINT uk_game_player UNIQUE (game_id, player_id);
```

## 📈 性能优化

### 索引策略
1. **主键索引**: 所有表都有自动主键索引
2. **外键索引**: 为所有外键字段创建索引
3. **查询索引**: 为常用查询字段创建复合索引
4. **时间索引**: 为时间戳字段创建索引

### 查询优化
```sql
-- 常用查询的优化索引
CREATE INDEX idx_room_active_participants ON core_roomparticipant(room_id, is_active, role);
CREATE INDEX idx_user_active_rooms ON core_roomparticipant(user_id, is_active, joined_at);
CREATE INDEX idx_template_steps ON events_eventstep(template_id, order_num, step_type);
```

### 数据清理
```sql
-- 定期清理过期房间
DELETE FROM core_room 
WHERE status = 'CLOSED' 
  AND closed_at < NOW() - INTERVAL '7 days';

-- 清理无效参与者记录
DELETE FROM core_roomparticipant 
WHERE left_at IS NOT NULL 
  AND left_at < NOW() - INTERVAL '30 days';
```

## 🔄 数据迁移

### 迁移文件组织
```
migrations/
├── core/
│   ├── 0001_initial.py
│   ├── 0002_add_user_groups.py
│   └── 0008_migrate_existing_data.py
├── events/
│   ├── 0001_initial.py
│   └── 0002_add_step_types.py
└── games/
    ├── 0001_initial.py
    └── 0002_add_pictionary.py
```

### 关键迁移
1. **用户组设置**: 创建标准用户组和权限
2. **数据迁移**: 迁移现有用户和房间数据
3. **索引优化**: 添加性能优化索引
4. **约束添加**: 添加数据完整性约束

---

**最后更新**: 2025-07-14
**维护者**: Augment AI Assistant
**版本**: 1.0
