# API参考文档

> **目标**: 为Augment提供完整的API接口文档，包括请求格式、响应示例、错误处理等

## 🔐 认证相关API

### 用户注册
```http
POST /api/register/
Content-Type: application/json

{
    "username": "string",
    "password": "string"
}
```

**响应示例**:
```json
{
    "id": 1,
    "username": "testuser",
    "subscription_level": "free"
}
```

### 用户登录
```http
POST /api/token/
Content-Type: application/json

{
    "username": "string", 
    "password": "string"
}
```

**响应示例**:
```json
{
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### Token刷新
```http
POST /api/token/refresh/
Content-Type: application/json

{
    "refresh": "refresh_token_string"
}
```

## 🏠 房间管理API

### 创建房间
```http
POST /api/rooms/create/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "template_id": 123
}
```

**响应示例**:
```json
{
    "id": 1,
    "room_code": "ABC123",
    "host": "username",
    "status": "OPEN",
    "participants": [
        {
            "username": "host_user",
            "role": "HOST",
            "score": 0,
            "state": "JOINED",
            "joined_at": "2025-07-14T10:00:00Z"
        }
    ],
    "event_template": {
        "id": 123,
        "name": "Template Name",
        "description": "Template Description"
    }
}
```

### 加入房间
```http
POST /api/rooms/join/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "room_code": "ABC123"
}
```

### 获取房间详情
```http
GET /api/rooms/{room_code}/
Authorization: Bearer {access_token}
```

### 预约房间
```http
POST /api/rooms/schedule/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "My Scheduled Room",
    "template_id": 123,
    "scheduled_start_time": "2025-12-25T14:30:00Z",
    "duration_hours": 2
}
```

## 📋 模板管理API

### 获取模板列表
```http
GET /api/events/templates/
Authorization: Bearer {access_token}
```

**响应示例**:
```json
[
    {
        "id": 1,
        "name": "Template Name",
        "description": "Template Description",
        "steps": [
            {
                "id": 1,
                "name": "Step 1",
                "order": 1,
                "step_type": "FREE_CHAT",
                "duration": 300
            }
        ]
    }
]
```

### 创建模板
```http
POST /api/events/templates/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "New Template",
    "description": "Template Description"
}
```

### 添加步骤到模板
```http
POST /api/events/templates/{id}/add-step/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "Step Name",
    "step_type": "FREE_CHAT",
    "duration": 300,
    "configuration": {}
}
```

### 重排模板步骤
```http
POST /api/events/templates/{id}/reorder-steps/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "step_ids": [3, 1, 2]
}
```

## 🎮 步骤管理API

### 获取步骤详情
```http
GET /api/events/steps/{id}/
Authorization: Bearer {access_token}
```

### 更新步骤
```http
PATCH /api/events/steps/{id}/
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "name": "Updated Step Name",
    "duration": 600
}
```

### 删除步骤
```http
DELETE /api/events/steps/{id}/
Authorization: Bearer {access_token}
```

## 💳 订阅管理API

### 获取订阅状态
```http
GET /api/subscription/
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
    "current_level": "free",
    "available_levels": ["free", "pro", "max"],
    "limits": {
        "max_participants": 10,
        "duration_hours": 2,
        "premium_features": false
    }
}
```

### 更新订阅等级 (调试模式)
```http
POST /api/subscription/
Authorization: Bearer {access_token}
X-Debug-Mode: true
Content-Type: application/json

{
    "target_level": "pro",
    "is_debug": true
}
```

## 📅 日历相关API

### 获取日历数据
```http
GET /api/calendar/
Authorization: Bearer {access_token}
```

### 获取房间模板列表
```http
GET /api/room-templates/
Authorization: Bearer {access_token}
```

## ❌ 错误响应格式

### 标准错误响应
```json
{
    "error": "Error message",
    "details": "Detailed error information"
}
```

### 验证错误响应
```json
{
    "error": "Validation failed",
    "field_errors": {
        "username": ["This field is required."],
        "password": ["Password too short."]
    }
}
```

### 权限错误响应
```json
{
    "error": "Permission denied",
    "required_subscription": "pro",
    "upgrade_required": true
}
```

## 📊 状态码说明

| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | 成功 | GET请求成功 |
| 201 | 创建成功 | POST请求创建资源成功 |
| 400 | 请求错误 | 参数验证失败、格式错误 |
| 401 | 未认证 | Token无效或缺失 |
| 403 | 权限不足 | 订阅等级不够、资源访问被拒绝 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 500 | 服务器错误 | 内部服务器错误 |

## 🔧 请求头说明

### 必需请求头
```http
Authorization: Bearer {access_token}  # 认证Token
Content-Type: application/json       # JSON请求
```

### 可选请求头
```http
X-Debug-Mode: true                   # 调试模式（仅开发环境）
```

## 📝 数据类型说明

### 房间状态枚举
- `SCHEDULED`: 已预约
- `OPEN`: 已开启
- `WAITING_FOR_HOST`: 等待房主
- `READY`: 准备就绪
- `IN_PROGRESS`: 进行中
- `ENDED`: 已结束
- `CLOSED`: 已关闭

### 用户状态枚举
- `JOINED`: 已加入
- `READY`: 准备就绪
- `PLAYING`: 游戏中
- `SPECTATING`: 观战中

### 步骤类型枚举
- `FREE_CHAT`: 自由聊天
- `GAME_PICTIONARY`: 你画我猜
- `GAME_UNDERCOVER`: 谁是卧底
- `VOTING`: 投票环节
- `RESULTS`: 结果展示

### 订阅等级枚举
- `free`: 免费用户
- `pro`: 专业用户
- `max`: 最高级用户

---

**最后更新**: 2025-07-14
**维护者**: Augment AI Assistant
**版本**: 1.0
