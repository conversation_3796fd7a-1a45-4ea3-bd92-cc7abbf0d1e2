# 团子项目 - Augment 开发指南

> **目标**: 为Augment AI助手提供快速理解项目上下文、定位需求变更位置的完整指南

## 📋 快速导航

- [项目概览](#项目概览) - 了解项目整体架构
- [核心模块](#核心模块) - 理解主要功能模块
- [常见需求映射](#常见需求映射) - 快速定位变更位置
- [开发工作流](#开发工作流) - 标准开发流程
- [测试策略](#测试策略) - 测试覆盖和验证
- [文档更新指南](#文档更新指南) - 如何维护此文档

## 🎯 项目概览

### 项目定位
团子是一个**实时多人游戏平台**，支持用户创建房间、加入游戏、进行实时互动。

### 技术栈
- **后端**: Django + Django REST Framework + WebSocket (Channels)
- **数据库**: PostgreSQL (生产) / SQLite (开发)
- **实时通信**: Django Channels + Redis
- **认证**: JWT Token
- **测试**: pytest + pytest-django + pytest-asyncio

### 核心概念
1. **房间 (Room)**: 游戏会话的容器，有状态机管理生命周期
2. **模板 (EventTemplate)**: 定义游戏流程的模板，包含多个步骤
3. **步骤 (EventStep)**: 游戏中的单个环节（如聊天、绘画、投票等）
4. **参与者 (RoomParticipant)**: 房间中的用户，有角色和状态
5. **订阅系统**: Free/Pro/Max三级订阅，控制功能访问

## 🏗️ 核心模块

### 1. 用户认证模块 (`core/`)
**文件位置**: `core/models.py`, `core/views.py`, `core/serializers.py`

**核心功能**:
- 用户注册/登录 (`RegisterView`, `CustomTokenObtainPairView`)
- JWT Token管理 (access/refresh token)
- 订阅等级管理 (Free/Pro/Max)
- 用户组权限系统

**常见需求**:
- 添加新的用户字段 → `core/models.py` User模型
- 修改认证逻辑 → `core/views.py` 认证相关视图
- 调整权限控制 → `core/models.py` 用户组设置

### 2. 房间管理模块 (`core/`)
**文件位置**: `core/models.py`, `core/services/room_manager.py`, `core/views.py`

**核心功能**:
- 房间创建/加入 (`RoomCreateView`, `JoinRoomView`)
- 房间状态机 (SCHEDULED→OPEN→READY→IN_PROGRESS→ENDED→CLOSED)
- 参与者管理 (`RoomParticipant` 模型)
- 房间生命周期管理

**常见需求**:
- 修改房间状态 → `core/models.py` RoomState枚举
- 调整加入逻辑 → `core/services/room_manager.py`
- 添加房间功能 → `core/views.py` 房间相关视图

### 3. 事件模板模块 (`events/`)
**文件位置**: `events/models.py`, `events/views.py`, `events/serializers.py`

**核心功能**:
- 模板创建/编辑 (`EventTemplateViewSet`)
- 步骤管理 (`EventStepViewSet`)
- 步骤类型定义 (聊天、绘画、投票等)
- 订阅权限控制

**常见需求**:
- 添加新步骤类型 → `events/models.py` EventStep.STEP_TYPES
- 修改模板逻辑 → `events/views.py` 相关ViewSet
- 调整权限控制 → `events/models.py` 权限验证方法

### 4. 游戏逻辑模块 (`games/`)
**文件位置**: `games/models.py`, `games/handlers/`

**核心功能**:
- 具体游戏实现 (`PictionaryGame` 等)
- 游戏状态管理
- 得分系统 (`PlayerScore`)
- 游戏事件处理器

**常见需求**:
- 添加新游戏 → `games/models.py` 新游戏模型
- 修改游戏逻辑 → `games/handlers/` 对应处理器
- 调整得分规则 → `games/models.py` 得分相关模型

### 5. 实时通信模块 (`core/consumers.py`)
**文件位置**: `core/consumers.py`, `Tuanzi_Backend/asgi.py`

**核心功能**:
- WebSocket连接管理 (`RoomConsumer`)
- 实时消息广播
- 游戏状态同步
- 用户在线状态管理

**常见需求**:
- 添加新消息类型 → `core/consumers.py` 消息处理方法
- 修改广播逻辑 → `core/consumers.py` 广播相关方法
- 调整连接管理 → `core/consumers.py` 连接生命周期方法

## 🎯 常见需求映射

### 用户相关需求
| 需求类型 | 主要文件 | 关键位置 |
|---------|---------|---------|
| 添加用户字段 | `core/models.py` | User模型定义 |
| 修改注册逻辑 | `core/views.py` | RegisterView |
| 调整权限系统 | `core/models.py` | 用户组和权限设置 |
| 订阅功能变更 | `core/models.py` | User.subscription_level |

### 房间相关需求
| 需求类型 | 主要文件 | 关键位置 |
|---------|---------|---------|
| 房间状态调整 | `core/models.py` | RoomState枚举 |
| 加入逻辑修改 | `core/services/room_manager.py` | join_room方法 |
| 容量限制调整 | `core/models.py` | Room.set_limits_by_subscription |
| 生命周期管理 | `core/models.py` | Room模型方法 |

### 游戏相关需求
| 需求类型 | 主要文件 | 关键位置 |
|---------|---------|---------|
| 新游戏类型 | `games/models.py` | 新游戏模型 |
| 步骤类型添加 | `events/models.py` | EventStep.STEP_TYPES |
| 游戏逻辑修改 | `games/handlers/` | 对应游戏处理器 |
| 得分规则调整 | `games/models.py` | PlayerScore相关 |

### API相关需求
| 需求类型 | 主要文件 | 关键位置 |
|---------|---------|---------|
| 新API端点 | `*/views.py` | 对应应用的views |
| 序列化调整 | `*/serializers.py` | 对应序列化器 |
| URL路由修改 | `*/urls.py` | URL配置 |
| 权限控制 | `*/views.py` | permission_classes |

### 实时功能需求
| 需求类型 | 主要文件 | 关键位置 |
|---------|---------|---------|
| 新消息类型 | `core/consumers.py` | receive方法 |
| 广播逻辑 | `core/consumers.py` | 广播相关方法 |
| 连接管理 | `core/consumers.py` | connect/disconnect |
| 状态同步 | `core/consumers.py` | 状态广播方法 |

## 🔄 开发工作流

### 1. 需求分析阶段
1. **确定需求类型** - 参考[常见需求映射](#常见需求映射)
2. **定位主要文件** - 根据功能模块确定修改位置
3. **检查依赖关系** - 确认是否影响其他模块
4. **评估测试需求** - 确定需要添加/修改的测试

### 2. 开发实施阶段
1. **模型层修改** - 如需要，先修改数据模型
2. **业务逻辑实现** - 在对应的views/services中实现
3. **API接口调整** - 更新序列化器和视图
4. **实时功能集成** - 如需要，更新WebSocket处理

### 3. 测试验证阶段
1. **单元测试** - 为新功能添加单元测试
2. **集成测试** - 验证模块间交互
3. **API测试** - 验证接口功能
4. **实时功能测试** - 验证WebSocket通信

### 4. 文档更新阶段
1. **更新此指南** - 添加新的需求映射
2. **更新API文档** - 记录新的接口
3. **更新变更日志** - 记录重要变更

## 📊 测试策略

### 测试文件组织
```
test/
├── test_core_models.py      # 核心模型测试
├── test_events_models.py    # 事件模型测试
├── test_games_models.py     # 游戏模型测试
├── test_room_state_machine.py # 房间状态机测试
├── api/                     # API测试
├── communication/           # WebSocket测试
└── integration/             # 集成测试
```

### 测试运行命令
```bash
# 运行所有测试
python -m pytest test/ -v

# 运行特定模块测试
python -m pytest test/test_core_models.py -v

# 运行API测试
python -m pytest test/api/ -v

# 运行带标记的测试
python -m pytest -m "api" -v
```

## 📝 文档更新指南

### 何时更新此文档
1. **添加新功能模块** - 更新核心模块部分
2. **修改重要架构** - 更新项目概览
3. **添加新的常见需求** - 更新需求映射表
4. **改变开发流程** - 更新开发工作流

### 如何更新
1. **保持结构一致** - 遵循现有的文档结构
2. **更新映射表** - 确保需求映射表的准确性
3. **添加示例** - 为复杂变更提供示例
4. **验证链接** - 确保所有内部链接有效

### 更新原则
- **及时性**: 在完成功能开发后立即更新
- **准确性**: 确保信息与代码实际情况一致
- **完整性**: 覆盖所有重要的变更点
- **可读性**: 保持清晰的结构和表述

---

**最后更新**: 2025-07-14
**维护者**: Augment AI Assistant
**版本**: 1.0
