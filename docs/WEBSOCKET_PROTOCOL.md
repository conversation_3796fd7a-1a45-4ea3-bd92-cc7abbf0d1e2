# WebSocket通信协议

> **目标**: 为Augment提供完整的WebSocket通信协议文档，包括连接管理、消息格式、事件类型等

## 🔌 连接管理

### 连接URL
```
ws://localhost:8000/ws/room/{room_code}/?token={jwt_token}
```

### 连接流程
1. **建立连接**: 客户端发起WebSocket连接请求
2. **Token验证**: 服务器验证JWT token有效性
3. **房间验证**: 验证用户是否有权限访问该房间
4. **加入房间组**: 将连接加入对应的房间组
5. **发送初始状态**: 向客户端发送房间当前状态

### 连接状态管理
- **心跳检测**: 每30秒发送ping/pong消息
- **自动重连**: 客户端在连接断开时自动重连
- **优雅断开**: 用户离开时发送断开消息

## 📨 消息格式

### 基础消息结构
```json
{
    "type": "message_type",
    "payload": {
        "key": "value"
    },
    "timestamp": "2025-07-14T10:00:00Z",
    "sender": "username"
}
```

### 消息类型分类
1. **系统消息**: 房间状态、用户加入/离开
2. **游戏消息**: 游戏开始、轮次变化、得分更新
3. **交互消息**: 聊天、绘画、投票
4. **控制消息**: 心跳、错误、确认

## 🏠 房间相关消息

### 用户加入房间
**服务器 → 客户端**
```json
{
    "type": "user_joined",
    "payload": {
        "username": "new_user",
        "role": "PARTICIPANT",
        "total_participants": 3
    }
}
```

### 用户离开房间
**服务器 → 客户端**
```json
{
    "type": "user_left",
    "payload": {
        "username": "leaving_user",
        "total_participants": 2
    }
}
```

### 房间状态变更
**服务器 → 客户端**
```json
{
    "type": "room_state_changed",
    "payload": {
        "old_state": "READY",
        "new_state": "IN_PROGRESS",
        "room_code": "ABC123"
    }
}
```

### 房间信息同步
**服务器 → 客户端**
```json
{
    "type": "room_state",
    "payload": {
        "room_code": "ABC123",
        "status": "READY",
        "host": "host_user",
        "participants": [
            {
                "username": "user1",
                "role": "HOST",
                "state": "READY",
                "score": 0
            }
        ]
    }
}
```

## 🎮 游戏相关消息

### 游戏开始
**客户端 → 服务器**
```json
{
    "type": "start_game",
    "payload": {}
}
```

**服务器 → 客户端**
```json
{
    "type": "game_started",
    "payload": {
        "game_type": "pictionary",
        "current_step": {
            "id": 1,
            "name": "Drawing Round",
            "step_type": "GAME_PICTIONARY",
            "duration": 300
        }
    }
}
```

### 轮次变化
**服务器 → 客户端**
```json
{
    "type": "turn_changed",
    "payload": {
        "current_player": "drawer_user",
        "role": "drawer",
        "word": "elephant",
        "time_remaining": 300
    }
}
```

### 得分更新
**服务器 → 客户端**
```json
{
    "type": "score_updated",
    "payload": {
        "username": "player1",
        "new_score": 150,
        "score_change": 50,
        "reason": "correct_guess"
    }
}
```

## 💬 聊天相关消息

### 发送聊天消息
**客户端 → 服务器**
```json
{
    "type": "chat_message",
    "payload": {
        "message": "Hello everyone!"
    }
}
```

**服务器 → 客户端**
```json
{
    "type": "chat_message",
    "payload": {
        "username": "sender_user",
        "message": "Hello everyone!",
        "timestamp": "2025-07-14T10:00:00Z"
    }
}
```

### 系统消息
**服务器 → 客户端**
```json
{
    "type": "system_message",
    "payload": {
        "message": "Game will start in 10 seconds",
        "level": "info"
    }
}
```

## 🎨 绘画相关消息

### 绘画数据
**客户端 → 服务器**
```json
{
    "type": "drawing_data",
    "payload": {
        "path_id": "path_123",
        "path": "M10,10 L20,20 L30,15",
        "color": "#000000",
        "stroke_width": 2
    }
}
```

**服务器 → 客户端**
```json
{
    "type": "drawing_update",
    "payload": {
        "drawer": "artist_user",
        "path_id": "path_123",
        "path": "M10,10 L20,20 L30,15",
        "color": "#000000",
        "stroke_width": 2
    }
}
```

### 清空画布
**客户端 → 服务器**
```json
{
    "type": "clear_canvas",
    "payload": {}
}
```

**服务器 → 客户端**
```json
{
    "type": "canvas_cleared",
    "payload": {
        "cleared_by": "artist_user"
    }
}
```

## 🗳️ 投票相关消息

### 发起投票
**服务器 → 客户端**
```json
{
    "type": "vote_started",
    "payload": {
        "vote_id": "vote_123",
        "question": "Who is the undercover?",
        "options": ["player1", "player2", "player3"],
        "duration": 30
    }
}
```

### 投票
**客户端 → 服务器**
```json
{
    "type": "cast_vote",
    "payload": {
        "vote_id": "vote_123",
        "option": "player2"
    }
}
```

### 投票结果
**服务器 → 客户端**
```json
{
    "type": "vote_results",
    "payload": {
        "vote_id": "vote_123",
        "results": {
            "player1": 1,
            "player2": 3,
            "player3": 0
        },
        "winner": "player2"
    }
}
```

## 🔧 控制消息

### 心跳检测
**服务器 ↔ 客户端**
```json
{
    "type": "ping",
    "payload": {}
}
```

```json
{
    "type": "pong", 
    "payload": {}
}
```

### 错误消息
**服务器 → 客户端**
```json
{
    "type": "error",
    "payload": {
        "code": "PERMISSION_DENIED",
        "message": "You don't have permission to perform this action",
        "details": "Only the host can start the game"
    }
}
```

### 确认消息
**服务器 → 客户端**
```json
{
    "type": "ack",
    "payload": {
        "message_id": "msg_123",
        "status": "received"
    }
}
```

## 🔄 消息流示例

### 完整游戏流程
```
1. 用户连接WebSocket
   → user_joined

2. 房主开始游戏
   client → start_game
   server → game_started

3. 游戏进行中
   server → turn_changed
   client → drawing_data
   server → drawing_update
   client → chat_message (猜测)
   server → chat_message
   server → score_updated

4. 游戏结束
   server → game_ended
   server → room_state_changed
```

## ⚠️ 错误处理

### 常见错误类型
- `INVALID_MESSAGE_FORMAT`: 消息格式错误
- `PERMISSION_DENIED`: 权限不足
- `ROOM_NOT_FOUND`: 房间不存在
- `GAME_NOT_ACTIVE`: 游戏未激活
- `RATE_LIMIT_EXCEEDED`: 消息频率过高

### 错误恢复策略
1. **重连机制**: 连接断开时自动重连
2. **状态同步**: 重连后请求最新状态
3. **消息重发**: 对重要消息进行重发
4. **降级处理**: 在网络不稳定时降低消息频率

## 📊 性能优化

### 消息优化
- **批量发送**: 将多个小消息合并发送
- **压缩传输**: 对大消息进行压缩
- **增量更新**: 只发送变化的数据
- **优先级队列**: 重要消息优先发送

### 连接优化
- **连接池**: 复用WebSocket连接
- **负载均衡**: 分散连接到多个服务器
- **资源清理**: 及时清理断开的连接
- **内存管理**: 控制消息缓存大小

---

**最后更新**: 2025-07-14
**维护者**: Augment AI Assistant
**版本**: 1.0
