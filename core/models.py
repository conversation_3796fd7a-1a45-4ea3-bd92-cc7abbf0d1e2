from django.db import models
from django.conf import settings
from django.contrib.auth.models import AbstractUser, Group
from events.models import EventTemplate # Import EventTemplate from the events app
from django.core.cache import cache
import json

class User(AbstractUser):
    """
    扩展的用户模型

    在Django默认用户模型基础上添加订阅功能：
    - 订阅等级管理
    - 基于订阅等级的功能权限控制
    """

    # 订阅等级常量定义
    SUBSCRIPTION_FREE = 'Free'  # 免费版：基础功能
    SUBSCRIPTION_PRO = 'Pro'    # Pro版：高级功能
    SUBSCRIPTION_MAX = 'Max'    # Max版：全功能

    SUBSCRIPTION_CHOICES = [
        (SUBSCRIPTION_FREE, 'Free'),
        (SUBSCRIPTION_PRO, 'Pro'),
        (SUBSCRIPTION_MAX, 'Max'),
    ]

    # 用户订阅等级字段
    subscription_level = models.CharField(
        max_length=10,
        choices=SUBSCRIPTION_CHOICES,
        default=SUBSCRIPTION_FREE,
        help_text='用户订阅等级，决定可用功能范围'
    )

    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text=('The groups this user belongs to. A user will get all permissions '
                   'granted to each of their groups.'),
        related_name="core_user_set",
        related_query_name="user",
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name="core_user_permissions_set",
        related_query_name="user",
    )

    def __str__(self):
        return f"{self.username} ({self.subscription_level})"

    # 用户组权限检查方法
    def is_in_group(self, group_name):
        """检查用户是否属于指定组"""
        return self.groups.filter(name=group_name).exists()

    def is_administrator(self):
        """检查用户是否为管理员（替代is_superuser）"""
        return self.is_in_group('Super Administrators') or self.is_in_group('Administrators')

    def is_staff_member(self):
        """检查用户是否为工作人员（替代is_staff）"""
        return self.is_administrator() or self.is_in_group('Staff')

    def get_user_role(self):
        """获取用户的主要角色"""
        if self.is_in_group('Super Administrators'):
            return 'super_admin'
        elif self.is_in_group('Administrators'):
            return 'admin'
        elif self.is_in_group('Staff'):
            return 'staff'
        else:
            return 'user'


class RoomState(models.TextChoices):
    """
    房间状态枚举 - 严格按照状态机设计

    完整的房间生命周期状态：
    SCHEDULED -> OPEN -> WAITING_FOR_HOST/READY -> IN_PROGRESS -> ENDED -> CLOSED
    """
    SCHEDULED = 'SCHEDULED', '已预约'           # 房间已创建，但未到预定开始时间
    OPEN = 'OPEN', '已开启'                     # 已到达预定时间，房间为空，等待玩家加入
    WAITING_FOR_HOST = 'WAITING_FOR_HOST', '等待房主'  # 有非预约者加入，等待预约者或确定房主
    READY = 'READY', '准备就绪'                 # 房主已确定，等待房主开始活动
    IN_PROGRESS = 'IN_PROGRESS', '活动中'       # 房主已开始活动，正在进行游戏
    ENDED = 'ENDED', '已结束'                   # 活动正常结束，可能处于计分或总结页面
    CLOSED = 'CLOSED', '已关闭'                 # 房间生命周期结束，被系统回收


class UserState(models.TextChoices):
    """
    用户在房间中的状态枚举
    """
    JOINED = 'JOINED', '已加入'
    READY = 'READY', '准备就绪'
    PLAYING = 'PLAYING', '游戏中'
    SPECTATING = 'SPECTATING', '观战中'


class Room(models.Model):
    # 使用新的枚举定义
    STATUS_SCHEDULED = RoomState.SCHEDULED
    STATUS_OPEN = RoomState.OPEN
    STATUS_WAITING_FOR_HOST = RoomState.WAITING_FOR_HOST
    STATUS_READY = RoomState.READY
    STATUS_IN_PROGRESS = RoomState.IN_PROGRESS
    STATUS_ENDED = RoomState.ENDED
    STATUS_CLOSED = RoomState.CLOSED
    STATUS_CHOICES = RoomState.choices

    room_code = models.CharField(max_length=10, unique=True, blank=True)
    host = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='hosted_rooms')
    # 注意：participants现在通过RoomParticipant中间模型管理
    # 可以通过 room.room_participants.all() 或 room.get_participants() 访问
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_OPEN)
    created_at = models.DateTimeField(auto_now_add=True)

    # 房间生命周期时间管理
    expires_at = models.DateTimeField(null=True, blank=True, help_text='房间过期时间，超过此时间房间将被关闭')
    closed_at = models.DateTimeField(null=True, blank=True, help_text='房间关闭时间，用于计算销毁时间')
    review_started_at = models.DateTimeField(null=True, blank=True, help_text='复盘阶段开始时间')
    last_activity_at = models.DateTimeField(auto_now_add=True, help_text='最后活动时间，用于检测空房间')

    # 预约系统相关字段
    scheduled_start_time = models.DateTimeField(null=True, blank=True, help_text='预约的开始时间，仅对SCHEDULED状态的房间有效')

    # --- NEW: Link Room to an EventTemplate ---
    # This is the core of the refactor. Every room is now based on a template.
    event_template = models.ForeignKey(EventTemplate, on_delete=models.SET_NULL, null=True, blank=True)

    # --- NEW: Link Room to a SystemTemplate ---
    # For rooms created from system templates, store the system template ID
    system_template = models.ForeignKey('SystemTemplate', on_delete=models.SET_NULL, null=True, blank=True)

    # --- NEW: Track the current step of the event flow ---
    current_step_order = models.PositiveIntegerField(default=0)

    # --- NEW: Subscription-based limitations ---
    max_participants = models.PositiveIntegerField(default=10, help_text="Maximum number of participants allowed")
    duration_hours = models.PositiveIntegerField(default=2, help_text="Room duration in hours")

    def set_limits_by_subscription(self, user):
        """
        根据用户订阅等级设置房间限制

        参数：
        - user: 用户对象，包含订阅等级信息

        限制规则：
        - Free: 10人，2小时
        - Pro: 500人，24小时
        - Max: 2000人，72小时
        """
        if user.subscription_level == user.SUBSCRIPTION_FREE:
            self.max_participants = 10
            self.duration_hours = 2
        elif user.subscription_level == user.SUBSCRIPTION_PRO:
            self.max_participants = 500
            self.duration_hours = 24
        elif user.subscription_level == user.SUBSCRIPTION_MAX:
            self.max_participants = 2000  # 实际上相当于无限制
            self.duration_hours = 72

    def set_scheduled_expiry(self):
        """
        为预约房间设置过期时间
        过期时间 = 预约开始时间 + 持续时长
        """
        if self.scheduled_start_time and self.duration_hours:
            from datetime import timedelta
            self.expires_at = self.scheduled_start_time + timedelta(hours=self.duration_hours)

    def is_scheduled_ready(self):
        """
        检查预约房间是否已到开始时间
        """
        if self.status != RoomState.SCHEDULED or not self.scheduled_start_time:
            return False

        from django.utils import timezone
        return timezone.now() >= self.scheduled_start_time

    def __str__(self):
        template_name = self.event_template.name if self.event_template else "No Template"
        status_display = f"[{self.get_status_display()}]"
        if self.status == RoomState.SCHEDULED and self.scheduled_start_time:
            from django.utils import timezone
            scheduled_time = timezone.localtime(self.scheduled_start_time).strftime('%m-%d %H:%M')
            status_display = f"[预约:{scheduled_time}]"
        return f"Room {self.room_code} {status_display} ({template_name})"

    def get_participants(self):
        """获取所有活跃的参与者用户对象"""
        return User.objects.filter(
            user_participations__room=self,
            user_participations__is_active=True
        )

    def get_participant_count(self):
        """获取活跃参与者数量"""
        return self.room_participants.filter(is_active=True).count()

    def add_participant(self, user, role='participant'):
        """添加参与者到房间"""
        # 检查用户是否已经在房间中
        existing = self.room_participants.filter(user=user, is_active=True).first()
        if existing:
            return existing

        # 如果用户之前退出过，重新激活
        previous = self.room_participants.filter(user=user).first()
        if previous:
            previous.is_active = True
            previous.role = role
            previous.save()
            return previous

        # 创建新的参与关系
        participant = RoomParticipant.objects.create(
            room=self,
            user=user,
            role=role
        )
        return participant

    def remove_participant(self, user):
        """从房间中移除参与者（设为非活跃状态）"""
        participant = self.room_participants.filter(user=user, is_active=True).first()
        if participant:
            participant.is_active = False
            participant.save()
            return True
        return False

    def get_host_participant(self):
        """获取房主的参与者记录"""
        return self.room_participants.filter(role='host', is_active=True).first()

    def get_participant_count(self):
        """获取活跃参与者数量"""
        return self.room_participants.filter(left_at__isnull=True).count()

    def is_full(self):
        """检查房间是否已满"""
        return self.get_participant_count() >= self.max_participants

    def can_join(self):
        """检查房间是否可以加入"""
        # 房间已关闭不能加入
        if self.status == RoomState.CLOSED:
            return False

        # 房间已满不能加入
        if self.is_full():
            return False

        # 房间已过期不能加入
        if self.is_expired():
            return False

        return True

    def is_expired(self):
        """检查房间是否已过期"""
        if not self.expires_at:
            return False
        from django.utils import timezone
        return timezone.now() > self.expires_at

class RoomParticipant(models.Model):
    """
    玩家-房间关系中间模型

    整合了原来的简单多对多关系和得分系统，提供更丰富的功能：
    - 加入时间和活跃状态跟踪
    - 房间内角色权限管理
    - 游戏得分和状态管理
    - 为未来功能预留扩展空间
    """

    # 基本关系
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='room_participants')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='user_participations')

    # 时间信息
    joined_at = models.DateTimeField(auto_now_add=True, help_text='用户加入房间的时间')
    last_active_at = models.DateTimeField(auto_now=True, help_text='用户最后活跃时间')

    # 房间内角色权限
    ROLE_PARTICIPANT = 'participant'
    ROLE_MODERATOR = 'moderator'
    ROLE_HOST = 'host'

    ROLE_CHOICES = [
        (ROLE_PARTICIPANT, '参与者'),
        (ROLE_MODERATOR, '协管员'),
        (ROLE_HOST, '房主'),
    ]

    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default=ROLE_PARTICIPANT,
        help_text='用户在房间中的角色'
    )

    # 游戏相关信息
    score = models.IntegerField(default=0, help_text='用户在房间中的总得分')

    # 用户状态（使用新的状态枚举）
    state = models.CharField(
        max_length=20,
        choices=UserState.choices,
        default=UserState.JOINED,
        help_text='用户在房间中的状态'
    )

    # 活跃状态信息
    is_active = models.BooleanField(default=True, help_text='用户是否仍在房间中（未退出）')
    left_at = models.DateTimeField(null=True, blank=True, help_text='用户离开房间的时间')

    # 扩展字段（为未来功能预留）
    custom_data = models.JSONField(default=dict, blank=True, help_text='自定义数据字段，用于存储特殊游戏状态等')

    class Meta:
        unique_together = ('room', 'user')
        ordering = ['-joined_at']
        verbose_name = '房间参与者'
        verbose_name_plural = '房间参与者'

    def __str__(self):
        return f"{self.user.username} in Room {self.room.room_code} ({self.get_role_display()})"

    def can_manage_room(self):
        """检查用户是否有房间管理权限"""
        return self.role in [self.ROLE_HOST, self.ROLE_MODERATOR]

    def can_control_game(self):
        """检查用户是否可以控制游戏流程"""
        return self.role == self.ROLE_HOST

    def add_score(self, points):
        """增加得分"""
        self.score += points
        self.save(update_fields=['score'])

    def reset_score(self):
        """重置得分"""
        self.score = 0
        self.save(update_fields=['score'])


# Note: GameSession might become obsolete or be refactored later,
# as the game logic will be driven by EventSteps. We'll keep it for now.
class GameSession(models.Model):
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='game_sessions')
    is_active = models.BooleanField(default=True)


class SystemTemplate(models.Model):
    """
    系统默认模板 - 不可修改、不可删除的预设模板

    这些模板对所有用户可见，提供常用的活动模板
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    # 模板配置，存储步骤信息
    template_config = models.JSONField(default=dict, help_text="模板的步骤配置")

    # 订阅等级要求
    SUBSCRIPTION_FREE = 'FREE'
    SUBSCRIPTION_PRO = 'PRO'
    SUBSCRIPTION_MAX = 'MAX'

    SUBSCRIPTION_CHOICES = [
        (SUBSCRIPTION_FREE, '免费版'),
        (SUBSCRIPTION_PRO, 'Pro版'),
        (SUBSCRIPTION_MAX, 'Max版'),
    ]

    required_subscription = models.CharField(
        max_length=10,
        choices=SUBSCRIPTION_CHOICES,
        default=SUBSCRIPTION_FREE,
        help_text="使用此模板所需的最低订阅等级"
    )

    class Meta:
        verbose_name = "系统模板"
        verbose_name_plural = "系统模板"
        ordering = ['id']

    def __str__(self):
        return f"[系统] {self.name}"

    def get_steps(self):
        """获取模板的步骤列表"""
        return self.template_config.get('steps', [])

    def is_accessible_by_user(self, user):
        """检查用户是否可以访问此模板"""
        if not self.is_active:
            return False

        # 检查订阅等级
        user_level = getattr(user, 'subscription_level', User.SUBSCRIPTION_FREE)

        # 用户订阅等级映射
        user_level_hierarchy = {
            User.SUBSCRIPTION_FREE: 0,
            User.SUBSCRIPTION_PRO: 1,
            User.SUBSCRIPTION_MAX: 2
        }

        # 系统模板订阅等级映射
        template_level_hierarchy = {
            self.SUBSCRIPTION_FREE: 0,
            self.SUBSCRIPTION_PRO: 1,
            self.SUBSCRIPTION_MAX: 2
        }

        required_level = template_level_hierarchy.get(self.required_subscription, 0)
        user_level_value = user_level_hierarchy.get(user_level, 0)

        return user_level_value >= required_level


class TemplateManager:
    """
    模板管理器 - 统一处理系统模板和用户模板的访问权限

    确保每个用户只能看到：
    1. 系统默认模板（所有用户可见）
    2. 自己创建的用户模板
    """

    @staticmethod
    def get_available_templates_for_user(user):
        """
        获取用户可用的所有模板

        Args:
            user: 用户对象

        Returns:
            list: 包含系统模板和用户模板的统一格式列表
        """
        templates = []

        # 1. 首先添加系统模板（显示在前面）
        system_templates = TemplateManager._get_system_templates_for_user(user)
        templates.extend(system_templates)

        # 2. 然后添加用户创建的模板
        from events.models import EventTemplate
        user_templates = EventTemplate.objects.filter(
            creator=user
        ).prefetch_related('steps').order_by('created_at')

        for template in user_templates:
            template_data = {
                'id': f'user_{template.id}',  # 新格式：user_1
                'type': 'user',
                'event_template_id': template.id,
                'name': template.name,
                'description': template.description,
                'creator_username': template.creator.username,
                'created_at': template.created_at.isoformat(),
                'steps': []
            }

            # 添加步骤信息
            for step in template.steps.all().order_by('order'):
                step_data = {
                    'id': step.id,
                    'name': step.name or step.get_step_type_display(),
                    'step_type': step.step_type,
                    'order': step.order,
                    'duration': step.duration,
                    'configuration': step.configuration,
                }
                template_data['steps'].append(step_data)

            templates.append(template_data)

        return templates

    @staticmethod
    def _get_system_templates_for_user(user):
        """
        获取用户可访问的系统模板（带缓存）

        Args:
            user: 用户对象

        Returns:
            list: 系统模板列表
        """
        # 尝试从缓存获取
        cache_key = f'system_templates_{user.subscription_level}'
        cached_templates = cache.get(cache_key)

        if cached_templates is not None:
            return json.loads(cached_templates)

        # 从数据库获取
        system_templates = []
        for template in SystemTemplate.objects.filter(is_active=True).order_by('id'):
            if template.is_accessible_by_user(user):
                template_data = {
                    'id': f'system_{template.id}',  # 新格式：system_1
                    'type': 'system',
                    'system_template_id': template.id,
                    'name': template.name,
                    'description': template.description,
                    'creator_username': 'System',
                    'created_at': template.created_at.isoformat(),
                    'required_subscription': template.required_subscription,
                    'steps': template.get_steps()
                }
                system_templates.append(template_data)

        # 缓存结果（缓存30分钟）
        cache.set(cache_key, json.dumps(system_templates), 1800)

        return system_templates

    @staticmethod
    def get_template_by_id(template_id, user):
        """
        根据模板ID获取模板对象，支持系统模板和用户模板

        Args:
            template_id: 模板ID，格式为 'system_1' 或 'user_1' 或纯数字
            user: 用户对象

        Returns:
            tuple: (template_obj, template_type) 或 (None, None)
        """
        try:
            # 处理新格式的模板ID
            if isinstance(template_id, str):
                if template_id.startswith('system_'):
                    # 系统模板
                    actual_id = int(template_id.replace('system_', ''))
                    template = SystemTemplate.objects.filter(
                        id=actual_id,
                        is_active=True
                    ).first()

                    if template and template.is_accessible_by_user(user):
                        return template, 'system'
                    else:
                        return None, None

                elif template_id.startswith('user_'):
                    # 提取用户模板ID
                    actual_id = int(template_id.replace('user_', ''))
                else:
                    # 兼容旧格式的纯数字ID
                    actual_id = int(template_id)
            else:
                # 数字ID
                actual_id = int(template_id)

            # 查找用户模板
            from events.models import EventTemplate
            template = EventTemplate.objects.filter(
                id=actual_id,
                creator=user
            ).prefetch_related('steps').first()

            if template:
                return template, 'user'
            else:
                return None, None

        except (ValueError, TypeError):
            # 无效的模板ID格式
            return None, None

    @staticmethod
    def copy_template_steps_to_room(template_obj, template_type, room):
        """
        将模板的步骤复制到房间

        Args:
            template_obj: 模板对象
            template_type: 模板类型 ('user' 或 'system')
            room: 房间对象
        """
        if template_type == 'user' and template_obj:
            # 对于用户模板，步骤信息已经存储在EventStep中
            # 房间的event_template字段已经设置，可以直接通过room.event_template.steps访问
            pass
        elif template_type == 'system' and template_obj:
            # 对于系统模板，步骤信息存储在template_config中
            # 房间的system_template字段已经设置，可以通过room.system_template.get_steps()访问
            pass
