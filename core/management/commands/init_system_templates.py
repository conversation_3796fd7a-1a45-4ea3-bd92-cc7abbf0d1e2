from django.core.management.base import BaseCommand
from core.models import SystemTemplate, User


class Command(BaseCommand):
    help = '初始化系统默认模板'

    def handle(self, *args, **options):
        self.stdout.write('开始初始化系统模板...')
        
        # 清除现有的系统模板（开发阶段）
        SystemTemplate.objects.all().delete()
        self.stdout.write('已清除现有系统模板')
        
        # 创建默认模板
        templates_created = 0
        
        # 1. 空白模板
        blank_template = SystemTemplate.objects.create(
            name='空白模板',
            description='一个空白的房间模板，适合自由讨论和临时活动',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={
                'steps': []
            }
        )
        templates_created += 1
        self.stdout.write(f'✓ 创建模板: {blank_template.name}')
        
        # 2. 你画我猜模板
        pictionary_template = SystemTemplate.objects.create(
            name='你画我猜 (30分钟)',
            description='经典的你画我猜游戏，持续30分钟，适合团队破冰和娱乐',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {
                        'id': 'step_1',
                        'name': '你画我猜游戏',
                        'step_type': 'GAME_PICTIONARY',
                        'order': 1,
                        'duration': 1800,  # 30分钟
                        'configuration': {
                            'rounds': 10,
                            'time_per_round': 180,  # 3分钟每轮
                            'difficulty': 'medium'
                        }
                    }
                ]
            }
        )
        templates_created += 1
        self.stdout.write(f'✓ 创建模板: {pictionary_template.name}')

        # 3. 自由讨论模板
        chat_template = SystemTemplate.objects.create(
            name='自由讨论 (15分钟)',
            description='简单的自由讨论环节，适合团队交流和分享',
            required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
            template_config={
                'steps': [
                    {
                        'id': 'step_1',
                        'name': '自由讨论',
                        'step_type': 'FREE_CHAT',
                        'order': 1,
                        'duration': 900,  # 15分钟
                        'configuration': {}
                    }
                ]
            }
        )
        templates_created += 1
        self.stdout.write(f'✓ 创建模板: {chat_template.name}')

        self.stdout.write(
            self.style.SUCCESS(f'成功创建 {templates_created} 个系统模板')
        )
        
        # 显示创建的模板
        self.stdout.write('\n创建的系统模板:')
        for template in SystemTemplate.objects.all():
            self.stdout.write(f'  - {template.name} ({template.required_subscription})')
            self.stdout.write(f'    {template.description}')
            steps_count = len(template.get_steps())
            self.stdout.write(f'    包含 {steps_count} 个步骤\n')
