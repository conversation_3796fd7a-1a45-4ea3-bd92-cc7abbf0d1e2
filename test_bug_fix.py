#!/usr/bin/env python
"""
简单的bug修复验证脚本
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

from django.contrib.auth import get_user_model
from core.models import Room, SystemTemplate, RoomParticipant, RoomState
from core.utils import advance_to_next_step
from core.models import TemplateManager

User = get_user_model()

def test_system_template_creation():
    """测试系统模板创建"""
    print("=== 测试系统模板创建 ===")
    
    # 清理现有数据
    SystemTemplate.objects.all().delete()
    
    # 创建测试系统模板
    template = SystemTemplate.objects.create(
        name='测试模板',
        description='用于测试的模板',
        required_subscription=SystemTemplate.SUBSCRIPTION_FREE,
        template_config={
            'steps': [
                {
                    'id': 'step_1',
                    'name': '自由讨论',
                    'step_type': 'FREE_CHAT',
                    'order': 1,
                    'duration': 300,
                    'configuration': {}
                },
                {
                    'id': 'step_2',
                    'name': '你画我猜',
                    'step_type': 'GAME_PICTIONARY',
                    'order': 2,
                    'duration': 600,
                    'configuration': {'rounds': 5}
                }
            ]
        }
    )
    
    print(f"✓ 创建系统模板: {template.name}")
    print(f"  步骤数量: {len(template.get_steps())}")
    return template

def test_template_manager():
    """测试模板管理器"""
    print("\n=== 测试模板管理器 ===")
    
    # 创建测试用户
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'password': 'testpass123',
            'subscription_level': User.SUBSCRIPTION_FREE
        }
    )
    
    # 获取可用模板
    templates = TemplateManager.get_available_templates_for_user(user)
    print(f"✓ 用户可用模板数量: {len(templates)}")
    
    for template in templates:
        print(f"  - {template['name']} ({template['type']}) ID: {template['id']}")
    
    return templates

def test_template_id_parsing():
    """测试模板ID解析"""
    print("\n=== 测试模板ID解析 ===")
    
    user = User.objects.get(username='testuser')
    
    # 测试系统模板ID
    system_templates = SystemTemplate.objects.all()
    if system_templates.exists():
        template_id = f"system_{system_templates.first().id}"
        template_obj, template_type = TemplateManager.get_template_by_id(template_id, user)
        
        if template_obj:
            print(f"✓ 系统模板ID解析成功: {template_id} -> {template_obj.name} ({template_type})")
        else:
            print(f"✗ 系统模板ID解析失败: {template_id}")

def test_room_creation_and_step_advance():
    """测试房间创建和步骤推进"""
    print("\n=== 测试房间创建和步骤推进 ===")
    
    user = User.objects.get(username='testuser')
    system_template = SystemTemplate.objects.first()
    
    if not system_template:
        print("✗ 没有可用的系统模板")
        return
    
    # 创建房间
    room = Room.objects.create(
        room_code='TEST01',
        host=user,
        system_template=system_template,
        status=RoomState.READY,
        current_step_order=0
    )
    
    print(f"✓ 创建房间: {room.room_code}")
    print(f"  关联系统模板: {room.system_template.name}")
    
    # 添加房主为参与者
    RoomParticipant.objects.create(
        room=room,
        user=user,
        role=RoomParticipant.ROLE_HOST,
        state='JOINED'
    )
    
    # 测试步骤推进
    print("\n--- 测试步骤推进 ---")
    
    # 推进到第一个步骤
    next_step = advance_to_next_step(room)
    if next_step:
        print(f"✓ 推进到步骤 {next_step.order}: {next_step.name} ({next_step.step_type})")
        print(f"  持续时间: {next_step.duration}秒")
    else:
        print("✗ 无法推进到下一步骤")
        return
    
    # 推进到第二个步骤
    next_step = advance_to_next_step(room)
    if next_step:
        print(f"✓ 推进到步骤 {next_step.order}: {next_step.name} ({next_step.step_type})")
        print(f"  配置: {next_step.configuration}")
    else:
        print("✗ 无法推进到第二个步骤")
        return
    
    # 尝试推进超出最后步骤
    next_step = advance_to_next_step(room)
    if next_step is None:
        room.refresh_from_db()
        print(f"✓ 所有步骤完成，房间状态: {room.status}")
    else:
        print("✗ 应该没有更多步骤了")

def main():
    """主函数"""
    print("开始bug修复验证测试...\n")
    
    try:
        # 测试系统模板创建
        test_system_template_creation()
        
        # 测试模板管理器
        test_template_manager()
        
        # 测试模板ID解析
        test_template_id_parsing()
        
        # 测试房间创建和步骤推进
        test_room_creation_and_step_advance()
        
        print("\n=== 测试完成 ===")
        print("✓ 所有测试通过！bug修复验证成功。")
        
    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
