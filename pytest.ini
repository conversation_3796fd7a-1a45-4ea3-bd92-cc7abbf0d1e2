[tool:pytest]
DJANGO_SETTINGS_MODULE = Tu<PERSON>zi_Backend.settings
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --reuse-db
    --nomigrations
    --asyncio-mode=auto
testpaths = test
markers =
    smoke: Smoke tests for basic functionality
    api: API endpoint tests
    logic: Business logic tests
    communication: WebSocket communication tests
    integration: End-to-end integration tests
    slow: Tests that take a long time to run
    unit: Unit tests
    functional: Functional tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
